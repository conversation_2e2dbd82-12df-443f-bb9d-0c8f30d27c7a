.placeholder-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.page-header {
  background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
  color: white;
  padding: 2rem;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.back-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.page-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.page-main {
  max-width: 800px;
  margin: 0 auto;
  padding: 4rem 2rem;
}

.placeholder-content {
  background: white;
  padding: 4rem 3rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.placeholder-icon {
  font-size: 4rem;
  margin-bottom: 2rem;
}

.placeholder-content h2 {
  color: #333;
  font-size: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.placeholder-content p {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.placeholder-content ul {
  text-align: left;
  max-width: 400px;
  margin: 2rem auto;
  padding: 0;
  list-style: none;
}

.placeholder-content li {
  color: #555;
  font-size: 1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  padding-left: 2rem;
}

.placeholder-content li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #4CAF50;
  font-weight: bold;
}

.placeholder-content li:last-child {
  border-bottom: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-header {
    padding: 1.5rem;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .page-main {
    padding: 2rem 1rem;
  }
  
  .placeholder-content {
    padding: 2.5rem 2rem;
  }
  
  .placeholder-icon {
    font-size: 3rem;
  }
  
  .placeholder-content h2 {
    font-size: 1.5rem;
  }
}
