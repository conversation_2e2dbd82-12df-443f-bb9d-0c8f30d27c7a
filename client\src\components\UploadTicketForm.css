.upload-ticket-container {
  max-width: 500px;
  width: 100%;
}

.upload-method-selector {
  margin-bottom: 1.5rem;
}

.method-tabs {
  display: flex;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.method-tab {
  flex: 1;
  padding: 0.875rem 1rem;
  background: #f5f5f5;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #666;
  transition: all 0.3s ease;
  border-right: 1px solid #e0e0e0;
}

.method-tab:last-child {
  border-right: none;
}

.method-tab:hover {
  background: #eeeeee;
  color: #333;
}

.method-tab.active {
  background: #2196F3;
  color: white;
}

.upload-form {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  background: #fafafa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.form-group label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.form-group input[type="file"],
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input[type="file"]:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2196F3;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #f0f8ff;
  border-radius: 6px;
  border: 1px solid #2196F3;
}

.file-name {
  font-weight: 500;
  color: #1976D2;
}

.file-size {
  font-size: 0.85rem;
  color: #666;
}

.upload-button {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.upload-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.upload-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.message {
  padding: 1rem;
  border-radius: 8px;
  font-weight: 500;
  text-align: center;
  margin-top: 1rem;
}

.message.success {
  background: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #4caf50;
}

.message.error {
  background: #ffebee;
  color: #c62828;
  border: 1px solid #f44336;
}

/* Custom file input styling */
.form-group input[type="file"] {
  position: relative;
  cursor: pointer;
}

.form-group input[type="file"]::-webkit-file-upload-button {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  margin-right: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.form-group input[type="file"]::-webkit-file-upload-button:hover {
  background: #e0e0e0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .upload-form {
    gap: 0.25rem;
  }

  .upload-button {
    padding: 0.875rem 1.5rem;
  }
}
