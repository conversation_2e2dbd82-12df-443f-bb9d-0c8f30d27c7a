{"common": {"submit": "Submit", "submitting": "Submitting...", "add": "Add", "update": "Update", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "loading": "Loading...", "error": "Error", "success": "Success", "view": "View", "refresh": "Refresh", "exportCSV": "Export CSV", "backToHome": "Back to Home", "searchPlaceholder": "Search jobs..."}, "geofencing": {"manageGeofences": "Manage Geofences", "activeGeofences": "Active Geofences", "type": "Type", "latitude": "Latitude", "longitude": "Longitude", "radius": "Radius (meters)", "pickupGeofence": "Pickup Geofence", "dropoffGeofence": "Dropoff Geofence", "locationRequired": "Location must be within the geofence", "locationValidation": "Location Validation", "currentLocation": "Current Location", "distanceToGeofence": "Distance to Geofence", "geofenceStatus": "Geofence Status", "withinRange": "Within Range", "outOfRange": "Out of Range"}, "po": {"location": "Location", "pickupLocation": "Pickup Location", "dropoffLocation": "Dropoff Location", "createNew": "Create New Purchase Order", "edit": "Edit Purchase Order", "client": "Client", "hauler": "Hauler", "materialType": "Material Type", "jobDetails": "Job Details", "scheduledDate": "Scheduled Date", "status": {"open": "Open", "completed": "Completed", "pending": "Open", "approved": "Open", "rejected": "Open", "expired": "Open", "available": "Open", "accepted": "Open", "in_progress": "In Progress", "verified": "Verified"}, "statusGroup": {"pending": "Pending", "inProgress": "In Progress", "completed": "Completed"}, "noPendingJobs": "No pending jobs available", "noInProgressJobs": "No jobs in progress", "noCompletedJobs": "No completed jobs", "createdAt": "Created At", "updatedAt": "Updated At", "approvedAt": "Approved At", "haulerRates": "Hauler Rates", "resaleRates": "Resale Rates", "rate": "Rate", "unit": "Unit", "actions": "Actions", "approve": "Approve", "reject": "Reject", "verify": "Verify Completion", "reopen": "Reopen Job", "noOrders": "No purchase orders found"}, "errors": {"fetchRatesFailed": "Failed to fetch rates", "syncFailed": "Failed to sync data", "exportFailed": "Failed to export data"}, "rates": {"title": "Current Rates", "hauling": "Hauling Rates", "material": "Material Rates", "rate": "Rate", "unit": "Unit", "materialType": "Material Type", "effectiveDate": "Effective Date"}, "tickets": {"title": "Material Ticket", "backup": "Physical Backup Copy", "print": "Print Ticket", "ticketNumber": "Ticket Number", "poNumber": "Purchase Order", "materialType": "Material Type", "quantity": "Quantity", "date": "Date & Time", "location": "Location", "driverSignature": "Driver Signature", "status": {"pending": "Pending", "in-progress": "In Progress", "completed": "Completed", "cancelled": "Cancelled"}}, "admin": {"title": "Administration", "dashboard": "Admin Dashboard", "dashboardDescription": "View and manage purchase orders by status", "userManagement": "User Management", "apiSettings": "API Settings", "companySettings": "Company Settings", "systemLogs": "System Logs", "backupRestore": "Backup & Restore", "users": "Users", "roles": "Roles", "permissions": "Permissions", "addUser": "Add User", "editUser": "Edit User", "deleteUser": "Delete User", "name": "Name", "email": "Email", "role": "Role", "lastLogin": "Last Login", "status": "Status", "active": "Active", "inactive": "Inactive", "suspended": "Suspended", "apiKey": "API Key", "generateKey": "Generate New Key", "revokeKey": "Revoke Key", "webhooks": "Webhooks", "endpoints": "Endpoints", "companyName": "Company Name", "companyLogo": "Company Logo", "uploadLogo": "Upload Logo", "removeLogo": "Remove Logo", "contactInfo": "Contact Information", "address": "Address", "phone": "Phone", "website": "Website", "systemLogLevel": "Log Level", "downloadLogs": "Download Logs", "clearLogs": "Clear Logs", "createBackup": "Create Backup", "restoreBackup": "Restore from Backup", "backupHistory": "Backup History", "date": "Date", "size": "Size", "download": "Download", "restore": "Rest<PERSON>", "delete": "Delete"}, "trucker": {"noActiveJob": "No Active Job", "browseAvailableJobs": "Browse available jobs to get started.", "viewAvailableJobs": "View Available Jobs", "startRoute": "Start Route", "enRouteToJobsite": "En route to jobsite", "markAsArrived": "<PERSON> as Arrived", "startJob": "Start Job", "completeJob": "Complete Job", "jobGeofence": "Job Geo<PERSON>ce", "newTransaction": "New Transaction", "activeJob": "Active Job"}, "ui": {"materialTicketHistory": "Material Ticket History", "loadingTickets": "Loading tickets...", "devMode": "Dev Mode", "testLogin": "Test Login", "poLogin": "PO <PERSON>gin", "truckerLogin": "Trucker <PERSON>", "toggleDevMode": "Toggle Developer Mode", "materialticketing": "Material Ticketing", "materialticketingdesc": "Upload, manage, and process material tickets", "administration": "Administration", "administrationdesc": "Manage system settings and user accounts", "purchaseorders": "Purchase Orders", "purchaseordersdesc": "Manage and review purchase orders", "clients": "Clients", "clientsdesc": "View and manage client accounts"}, "navigation": {"adminDashboard": "Admin Dashboard", "adminDashboardDesc": "View and manage purchase orders by status", "administration": "Administration", "adminDesc": "Manage system settings and user accounts", "materialTicketing": "Material Ticketing", "ticketingDesc": "Upload, manage, and process material tickets", "purchaseOrders": "Purchase Orders", "poDesc": "Create and manage purchase orders", "mtoFlagship": "MTO's Flagship", "mtoDesc": "Access flagship features and tools", "ticketArchive": "Ticket Archive", "archiveDesc": "View and search archived tickets"}, "customerSignature": "Customer Signature", "footer": "This is a physical backup copy of the electronic ticket. Please keep for your records."}