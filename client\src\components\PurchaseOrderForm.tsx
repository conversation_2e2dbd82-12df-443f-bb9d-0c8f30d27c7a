import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { usePurchaseOrders } from '../contexts/PurchaseOrderContext';
import { PlusCircle, Trash2, MapPin } from 'lucide-react';
import './PurchaseOrderForm.css';

// Define interfaces for the component props and state
interface LocationPoint {
  lat: number;
  lng: number;
}

interface Geofence {
  _id: string;
  name: string;
  locationType: 'pickup' | 'dropoff';
  coordinates: LocationPoint[];
}

interface FormLocation {
  name: string;
  address: string;
  coordinates: LocationPoint;
}

// Define the structure for a load in the form
interface Load {
  materialType: string;
  quantity: number;
  unit: string;
  category?: string;
}

// Define the structure for a rate in the form
interface Rate {
  materialType: string;
  rate: number;
  unit: string;
}

// Props for the PurchaseOrderForm component
interface PurchaseOrderFormProps {
  geofences: Geofence[];
  isClientView?: boolean;
  initialData?: any; // Replace with proper type when available
}

// Geofence validation function
const validateLocation = (location: LocationPoint, geofences: Geofence[], locationType: 'pickup' | 'dropoff') => {
  // Check if the location is within any of the geofences of the specified type
  const relevantGeofences = geofences.filter(g => g.locationType === locationType);
  
  // In a real implementation, this would use turf.js to check if the point is within any geofence
  // For now, we'll just return true if there are no geofences or if the location has valid coordinates
  if (relevantGeofences.length === 0) return true;
  
  return location.lat !== 0 && location.lng !== 0;
};

// Sample predefined locations for dropdown selection
const samplePredefinedLocations: FormLocation[] = [
  {
    name: 'Boynton Beach Office',
    address: '1605 Renaissance Commons Blvd., Boynton Beach, FL 33426',
    coordinates: { lat: 26.5318, lng: -80.0905 }
  },
  {
    name: 'West Palm Beach Yard',
    address: '456 Industrial Ave, West Palm Beach, FL 33401',
    coordinates: { lat: 26.7153, lng: -80.0534 }
  },
  {
    name: 'Delray Beach Site',
    address: '789 Beach Rd, Delray Beach, FL 33483',
    coordinates: { lat: 26.4615, lng: -80.0728 }
  },
  {
    name: 'Boynton Beach City Hall',
    address: '100 E Ocean Ave, Boynton Beach, FL 33435',
    coordinates: { lat: 26.5253, lng: -80.0664 }
  },
  {
    name: 'Quantum Park',
    address: '1600 Corporate Dr, Boynton Beach, FL 33436',
    coordinates: { lat: 26.5396, lng: -80.1026 }
  }
];

// Simple Map Component to display locations
const LocationMap: React.FC<{
  locations: {
    name: string;
    coordinates: LocationPoint;
    type: 'pickup' | 'delivery';
  }[];
  height?: string;
}> = ({ locations, height = '300px' }) => {
  const mapRef = React.useRef<HTMLDivElement>(null);
  const [mapLoaded, setMapLoaded] = useState(false);

  useEffect(() => {
    // Load Google Maps script if not already loaded
    if (!window.google && !document.getElementById('google-maps-script')) {
      const script = document.createElement('script');
      script.id = 'google-maps-script';
      script.src = `https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&libraries=places`;
      script.async = true;
      script.defer = true;
      script.onload = () => setMapLoaded(true);
      document.head.appendChild(script);
    } else if (window.google) {
      setMapLoaded(true);
    }
  }, []);

  useEffect(() => {
    if (mapLoaded && mapRef.current && locations.length > 0) {
      const google = window.google;
      const bounds = new google.maps.LatLngBounds();
      
      // Create map
      const map = new google.maps.Map(mapRef.current, {
        zoom: 10,
        center: { lat: locations[0].coordinates.lat, lng: locations[0].coordinates.lng },
        mapTypeId: google.maps.MapTypeId.ROADMAP
      });

      // Add markers for each location
      locations.forEach(location => {
        if (location.coordinates.lat !== 0 && location.coordinates.lng !== 0) {
          const position = new google.maps.LatLng(
            location.coordinates.lat,
            location.coordinates.lng
          );
          
          bounds.extend(position);
          
          const icon = {
            url: `http://maps.google.com/mapfiles/ms/icons/${location.type === 'pickup' ? 'green' : 'red'}-dot.png`,
            scaledSize: new google.maps.Size(32, 32)
          };
          
          const marker = new google.maps.Marker({
            position,
            map,
            title: location.name,
            icon
          });
          
          const infoWindow = new google.maps.InfoWindow({
            content: `<div><strong>${location.name}</strong><br/>${location.type === 'pickup' ? 'Pickup Location' : 'Delivery Location'}</div>`
          });
          
          marker.addListener('click', () => {
            infoWindow.open(map, marker);
          });
        }
      });
      
      // Fit map to bounds if we have valid locations
      if (!bounds.isEmpty()) {
        map.fitBounds(bounds);
      }
    }
  }, [mapLoaded, locations]);

  return (
    <div className="location-map-container">
      <div ref={mapRef} style={{ width: '100%', height, borderRadius: '4px' }}></div>
      <div className="map-legend">
        <div className="legend-item">
          <div className="legend-icon pickup"></div>
          <span>Pickup Location</span>
        </div>
        <div className="legend-item">
          <div className="legend-icon delivery"></div>
          <span>Delivery Location</span>
        </div>
      </div>
    </div>
  );
};

// Add additional styles for the new form components
const additionalStyles = `
  .location-map-container {
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 0.5rem;
    background-color: #f9f9f9;
  }
  
  .map-legend {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
  }
  
  .legend-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
  
  .legend-icon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }
  
  .legend-icon.pickup {
    background-color: #4caf50;
  }
  
  .legend-icon.delivery {
    background-color: #f44336;
  }

  .section-header-with-button {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }
  
  .delivery-location-item, .load-item {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f9f9f9;
  }
  
  .location-header, .load-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }
  
  .add-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: #4caf50;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
  }
  
  .remove-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: #f44336;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
  }
  
  .form-row {
    display: flex;
    gap: 1rem;
  }
  
  .half-width {
    width: 50%;
  }
  
  .empty-state {
    text-align: center;
    padding: 2rem;
    color: #757575;
    background-color: #f5f5f5;
    border-radius: 4px;
  }
`;

// Material type categories and options
const materialTypeOptions = {
  'Aggregates & Base Materials': [
    'Crushed Concrete',
    'Crushed Asphalt',
    'ABC (Aggregate Base Course)',
    'Road Base',
    '#57 Stone',
    '#89 Stone',
    'Rip Rap',
    'Pea Gravel',
    'Fill Rock',
    'Oversized Rock'
  ],
  'Sand & Soil Products': [
    'Fill Sand',
    'Mason Sand',
    'Concrete Sand',
    'Screened Sand',
    'Topsoil – Unscreened',
    'Topsoil – Screened',
    'Organic Topsoil',
    'Sandy Loam',
    'Clay – Red or Gray',
    'Stabilized Sand'
  ],
  'Recycled / Specialty Materials': [
    'Recycled Asphalt Millings',
    'Recycled Concrete Base',
    'RAP (Reclaimed Asphalt Pavement)',
    'Flowable Fill (CLSM)',
    'Muck (Haul-off)',
    'Spoil Dirt',
    'Contaminated Soil',
    'Lime Rock Base',
    'Shell Rock'
  ],
  'Landscaping & Decorative': [
    'Mulch – Natural',
    'Mulch – Colored (Red, Brown, Black)',
    'River Rock – Small, Medium, Large',
    'Lava Rock',
    'Pine Bark Nuggets',
    'Potting Soil Mix'
  ],
  'Dust & Fines': [
    'Rock Fines',
    'Granite Fines',
    'Limestone Dust',
    'Screening Material'
  ],
  'Bulk Liquids & Additives': [
    'Water (Dust Control)',
    'Liquid Lime',
    'Slurry Cement'
  ],
  'Haul-Off / Disposal Types': [
    'Concrete Demo (Broken)',
    'Asphalt Demo',
    'Mixed Construction Debris',
    'Tree Debris / Vegetation',
    'Excess Dirt – Clean',
    'Excess Dirt – Contaminated',
    'Septic Pump-Out'
  ]
};

// Main component function
const PurchaseOrderForm: React.FC<PurchaseOrderFormProps> = ({ 
  geofences = [], 
  isClientView = false, 
  initialData 
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { createPurchaseOrder, updatePurchaseOrder } = usePurchaseOrders();
  const [loading, setLoading] = useState(false);
  
  // Basic PO details
  const [jobDetails, setJobDetails] = useState<string>(initialData?.jobDetails || '');
  const [poNumber, setPoNumber] = useState<string>(initialData?.poNumber || '');
  const [referenceNumber, setReferenceNumber] = useState<string>(initialData?.referenceNumber || '');
  
  // Rates
  const [haulerRates, setHaulerRates] = useState<Rate[]>(initialData?.haulerRates || []);
  const [resaleRates, setResaleRates] = useState<Rate[]>(initialData?.resaleRates || []);
  
  // State for starting (pickup) location
  const [startingLocation, setStartingLocation] = useState<FormLocation>(initialData?.startingLocation || {
    name: '',
    address: '',
    coordinates: { lat: 0, lng: 0 }
  });
  
  // State for delivery (dump) locations
  const [deliveryLocations, setDeliveryLocations] = useState<FormLocation[]>(initialData?.deliveryLocations || [{
    name: '',
    address: '',
    coordinates: { lat: 0, lng: 0 }
  }]);
  
  // State for loads (material type, quantity, unit)
  const [loads, setLoads] = useState<Load[]>(initialData?.loads || [{
    materialType: '',
    quantity: 0,
    unit: 'tons',
    category: ''
  }]);
  
  // Form validation errors
  const [locationErrors, setLocationErrors] = useState<{pickup?: string; dropoff?: string}>({});
  
  // Use the sample predefined locations
  const predefinedLocations = samplePredefinedLocations;
  
  // Function to validate all locations
  const validateLocations = () => {
    const errors: {pickup?: string; dropoff?: string} = {};
    
    // Validate starting location (pickup)
    if (!validateLocation(startingLocation.coordinates, geofences, 'pickup')) {
      errors.pickup = t('geofencing.pickupOutsideGeofence');
    }
    
    // Validate all delivery locations (dropoff)
    const hasInvalidDeliveryLocation = deliveryLocations.some(
      location => !validateLocation(location.coordinates, geofences, 'dropoff')
    );
    
    if (hasInvalidDeliveryLocation) {
      errors.dropoff = t('geofencing.dropoffOutsideGeofence');
    }
    
    setLocationErrors(errors);
    return Object.keys(errors).length === 0;
  };
  
  // Handler for rate changes
  const handleRateChange = (index: number, field: keyof Rate, value: string | number, isHaulerRate: boolean) => {
    const rates = isHaulerRate ? [...haulerRates] : [...resaleRates];
    rates[index] = { ...rates[index], [field]: value };
    
    if (isHaulerRate) {
      setHaulerRates(rates);
    } else {
      setResaleRates(rates);
    }
  };
  
  // Handler for adding a new rate
  const handleAddRate = (isHaulerRate: boolean) => {
    const newRate: Rate = { materialType: '', rate: 0, unit: 'tons' };
    if (isHaulerRate) {
      setHaulerRates([...haulerRates, newRate]);
    } else {
      setResaleRates([...resaleRates, newRate]);
    }
  };
  
  // Handler for starting location changes
  const handleStartingLocationChange = (field: string, value: string | number) => {
    if (field === 'locationSelect') {
      // When selecting from dropdown, update all location fields
      const selectedLocation = predefinedLocations.find(loc => loc.name === value);
      if (selectedLocation) {
        setStartingLocation({
          name: selectedLocation.name,
          address: selectedLocation.address,
          coordinates: selectedLocation.coordinates
        });
      }
    } else {
      // For manual edits to individual fields
      setStartingLocation(prev => ({
        ...prev,
        [field]: value,
        ...(field === 'lat' || field === 'lng' ? {
          coordinates: {
            ...prev.coordinates,
            [field]: typeof value === 'string' ? parseFloat(value) : value
          }
        } : {})
      }));
    }
  };
  
  // Handler for delivery location changes
  const handleDeliveryLocationChange = (index: number, field: string, value: string | number) => {
    if (field === 'locationSelect') {
      // When selecting from dropdown, update all location fields
      const selectedLocation = predefinedLocations.find(loc => loc.name === value);
      if (selectedLocation) {
        const updatedLocations = [...deliveryLocations];
        updatedLocations[index] = {
          name: selectedLocation.name,
          address: selectedLocation.address,
          coordinates: selectedLocation.coordinates
        };
        setDeliveryLocations(updatedLocations);
      }
    } else {
      // For manual edits to individual fields
      const updatedLocations = [...deliveryLocations];
      if (!updatedLocations[index]) {
        updatedLocations[index] = {
          name: '',
          address: '',
          coordinates: { lat: 0, lng: 0 }
        };
      }

      if (field === 'lat' || field === 'lng') {
        updatedLocations[index] = {
          ...updatedLocations[index],
          coordinates: {
            ...updatedLocations[index].coordinates,
            [field]: typeof value === 'string' ? parseFloat(value) : value
          }
        };
      } else {
        updatedLocations[index] = {
          ...updatedLocations[index],
          [field]: value
        };
      }

      setDeliveryLocations(updatedLocations);
    }
  };

  // Add a new delivery location
  const addDeliveryLocation = () => {
    setDeliveryLocations([...deliveryLocations, {
      name: '',
      address: '',
      coordinates: { lat: 0, lng: 0 }
    }]);
  };

  // Remove a delivery location
  const removeDeliveryLocation = (index: number) => {
    if (deliveryLocations.length <= 1) return; // Keep at least one delivery location
    
    const updatedLocations = [...deliveryLocations];
    updatedLocations.splice(index, 1);
    setDeliveryLocations(updatedLocations);
  };

  // Handler for load changes
  const handleLoadChange = (index: number, field: keyof Load | string, value: string | number) => {
    const updatedLoads = [...loads];
    if (!updatedLoads[index]) {
      updatedLoads[index] = { materialType: '', quantity: 0, unit: 'tons' };
    }
    
    updatedLoads[index] = { 
      ...updatedLoads[index], 
      [field]: field === 'quantity' ? (typeof value === 'string' ? parseFloat(value) : value) : value 
    };
    
    setLoads(updatedLoads);
  };

  // Add a new load
  const addLoad = () => {
    setLoads([...loads, { materialType: '', quantity: 0, unit: 'tons', category: '' }]);
  };

  // Remove a load
  const removeLoad = (index: number) => {
    if (loads.length <= 1) return; // Keep at least one load
    
    const updatedLoads = [...loads];
    updatedLoads.splice(index, 1);
    setLoads(updatedLoads);
  };
  
  // Form submission handler
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateLocations()) {
      return;
    }
    
    // Validate required fields
    if (startingLocation.name === '' || deliveryLocations.length === 0 || loads.length === 0) {
      alert(t('po.missingRequiredFields'));
      return;
    }
    
    setLoading(true);
    
    const purchaseOrderData = {
      jobDetails,
      poNumber,
      referenceNumber,
      haulerRates,
      resaleRates,
      startingLocation,
      deliveryLocations,
      loads,
      status: 'open' // Default status for new POs - only admin can close
    };
    
    const saveOperation = initialData?._id 
      ? updatePurchaseOrder(initialData._id, purchaseOrderData)
      : createPurchaseOrder(purchaseOrderData);
    
    saveOperation
      .then(() => {
        navigate('/purchase-orders');
      })
      .catch(error => {
        console.error('Error saving purchase order:', error);
        alert(t('po.errorSaving'));
      })
      .finally(() => {
        setLoading(false);
      });
  };
  
  // Render the form
  return (
    <div className="po-form-container">
      <header className="page-header">
        <div className="header-content">
          <button className="back-button" onClick={() => navigate('/purchase-orders')}>
            ← {t('common.back')}
          </button>
          <div className="page-brand">
            <div className="page-title">
              <h1>{isClientView ? t('po.approve') : t('po.create')}</h1>
              <p>{isClientView ? t('po.clientApproval') : t('po.jobDetails')}</p>
            </div>
          </div>
        </div>
      </header>

      <div className="po-form-content">
        <form onSubmit={handleSubmit} className="po-form">
          {/* PO Details Section */}
          <section className="form-section">
            <h2>{t('po.jobDetails')}</h2>
            <div className="form-group">
              <label>{t('po.poNumber')}</label>
              <input
                type="text"
                value={poNumber}
                onChange={(e) => setPoNumber(e.target.value)}
                className="form-input"
                required
              />
            </div>
            <div className="form-group">
              <label>{t('po.referenceNumber')}</label>
              <input
                type="text"
                value={referenceNumber}
                onChange={(e) => setReferenceNumber(e.target.value)}
                className="form-input"
                required
              />
            </div>
            <div className="form-group">
              <label>{t('po.jobDetails')}</label>
              <textarea
                value={jobDetails}
                onChange={(e) => setJobDetails(e.target.value)}
                className="form-textarea"
                rows={4}
                required
              />
            </div>
          </section>

          {/* Hauler Rates Section */}
          {!isClientView && (
            <section className="form-section">
              <div className="section-header-with-button">
                <h2>{t('po.haulerRates')}</h2>
                <button
                  type="button"
                  onClick={() => handleAddRate(true)}
                  className="add-button"
                >
                  <PlusCircle size={16} />
                  {t('po.addRate')}
                </button>
              </div>

              {haulerRates.length === 0 ? (
                <div className="empty-state">
                  {t('po.noRates')}
                </div>
              ) : (
                <div className="rates-grid">
                  {haulerRates.map((rate: Rate, index: number) => (
                    <div key={index} className="rate-card">
                      <input
                        type="text"
                        placeholder={t('ticket.materialType')}
                        value={rate.materialType}
                        onChange={(e) => handleRateChange(index, 'materialType', e.target.value, true)}
                        className="form-input"
                        required
                      />
                      <input
                        type="number"
                        placeholder={t('po.rate')}
                        value={rate.rate}
                        onChange={(e) => handleRateChange(index, 'rate', parseFloat(e.target.value), true)}
                        className="form-input"
                        required
                      />
                      <input
                        type="text"
                        placeholder={t('ticket.unit')}
                        value={rate.unit}
                        onChange={(e) => handleRateChange(index, 'unit', e.target.value, true)}
                        className="form-input"
                        required
                      />
                    </div>
                  ))}
                </div>
              )}
            </section>
          )}

          {/* Client Rates Section */}
          {isClientView && (
            <section className="form-section">
              <div className="section-header-with-button">
                <h2>{t('po.clientRates')}</h2>
                <button
                  type="button"
                  onClick={() => handleAddRate(false)}
                  className="add-button"
                >
                  <PlusCircle size={16} />
                  {t('po.addClientRate')}
                </button>
              </div>

              {resaleRates.length === 0 ? (
                <div className="empty-state">
                  {t('po.noClientRates')}
                </div>
              ) : (
                <div className="rates-grid">
                  {resaleRates.map((rate: Rate, index: number) => (
                    <div key={index} className="rate-card">
                      <input
                        type="text"
                        placeholder={t('ticket.materialType')}
                        value={rate.materialType}
                        onChange={(e) => handleRateChange(index, 'materialType', e.target.value, false)}
                        className="form-input"
                        required
                      />
                      <input
                        type="number"
                        placeholder={t('po.rate')}
                        value={rate.rate}
                        onChange={(e) => handleRateChange(index, 'rate', parseFloat(e.target.value), false)}
                        className="form-input"
                        required
                      />
                      <input
                        type="text"
                        placeholder={t('ticket.unit')}
                        value={rate.unit}
                        onChange={(e) => handleRateChange(index, 'unit', e.target.value, false)}
                        className="form-input"
                        required
                      />
                    </div>
                  ))}
                </div>
              )}
            </section>
          )}

          {/* Map visualization of all locations */}
          <section className="form-section">
            <h2>{t('po.locationMap')}</h2>
            <LocationMap 
              locations={[
                { 
                  name: startingLocation.name || t('po.pickupLocation'), 
                  coordinates: startingLocation.coordinates,
                  type: 'pickup'
                },
                ...deliveryLocations.map(loc => ({
                  name: loc.name || t('po.deliveryLocation'),
                  coordinates: loc.coordinates,
                  type: 'delivery' as const
                }))
              ]} 
              height="400px"
            />
          </section>

          {/* Starting Location Section */}
          <section className="form-section">
            <div className="section-header-with-button">
              <h2>{t('po.startingLocation')}</h2>
            </div>
            <div className="form-group">
              <label>{t('po.selectLocation')}</label>
              <select
                value={startingLocation.name || ''}
                onChange={(e) => handleStartingLocationChange('locationSelect', e.target.value)}
                className="form-input"
              >
                <option value="">{t('po.selectLocationPrompt')}</option>
                {predefinedLocations.map((location, idx) => (
                  <option key={idx} value={location.name}>{location.name}</option>
                ))}
                <option value="custom">{t('po.customLocation')}</option>
              </select>
            </div>
            <div className="form-group">
              <label>{t('po.locationName')}</label>
              <input
                type="text"
                value={startingLocation.name}
                onChange={(e) => handleStartingLocationChange('name', e.target.value)}
                className="form-input"
                required
              />
            </div>
            <div className="form-group">
              <label>{t('po.address')}</label>
              <input
                type="text"
                value={startingLocation.address}
                onChange={(e) => handleStartingLocationChange('address', e.target.value)}
                className="form-input"
                required
              />
            </div>
            <div className="form-row">
              <div className="form-group half-width">
                <label>{t('geofencing.latitude')}</label>
                <input
                  type="number"
                  step="any"
                  value={startingLocation.coordinates.lat}
                  onChange={(e) => handleStartingLocationChange('lat', e.target.value)}
                  className="form-input"
                  required
                />
              </div>
              <div className="form-group half-width">
                <label>{t('geofencing.longitude')}</label>
                <input
                  type="number"
                  step="any"
                  value={startingLocation.coordinates.lng}
                  onChange={(e) => handleStartingLocationChange('lng', e.target.value)}
                  className="form-input"
                  required
                />
              </div>
            </div>
            {locationErrors.pickup && (
              <span className="error-message">{locationErrors.pickup}</span>
            )}
          </section>
          
          {/* Delivery Locations Section */}
          <section className="form-section">
            <div className="section-header-with-button">
              <h2>{t('po.deliveryLocations')}</h2>
              <button 
                type="button" 
                className="add-button" 
                onClick={addDeliveryLocation}
              >
                <PlusCircle size={16} />
                {t('po.addLocation')}
              </button>
            </div>
            
            {deliveryLocations.length === 0 ? (
              <div className="empty-state">
                {t('po.noDeliveryLocations')}
              </div>
            ) : (
              deliveryLocations.map((location: FormLocation, index: number) => (
                <div className="delivery-location-item" key={index}>
                  <div className="location-header">
                    <h3>{t('po.deliveryLocation')} #{index + 1}</h3>
                    <button 
                      type="button" 
                      className="remove-button"
                      onClick={() => removeDeliveryLocation(index)}
                      disabled={deliveryLocations.length <= 1}
                    >
                      <Trash2 size={14} />
                      {t('common.remove')}
                    </button>
                  </div>
                  <div className="form-group">
                    <label>{t('po.selectLocation')}</label>
                    <select
                      value={location.name || ''}
                      onChange={(e) => handleDeliveryLocationChange(index, 'locationSelect', e.target.value)}
                      className="form-input"
                    >
                      <option value="">{t('po.selectLocationPrompt')}</option>
                      {predefinedLocations.map((loc, idx) => (
                        <option key={idx} value={loc.name}>{loc.name}</option>
                      ))}
                      <option value="custom">{t('po.customLocation')}</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>{t('po.locationName')}</label>
                    <input
                      type="text"
                      value={location.name}
                      onChange={(e) => handleDeliveryLocationChange(index, 'name', e.target.value)}
                      className="form-input"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>{t('po.address')}</label>
                    <input
                      type="text"
                      value={location.address}
                      onChange={(e) => handleDeliveryLocationChange(index, 'address', e.target.value)}
                      className="form-input"
                      required
                    />
                  </div>
                  <div className="form-row">
                    <div className="form-group half-width">
                      <label>{t('geofencing.latitude')}</label>
                      <input
                        type="number"
                        step="any"
                        value={location.coordinates.lat}
                        onChange={(e) => handleDeliveryLocationChange(index, 'lat', e.target.value)}
                        className="form-input"
                        required
                      />
                    </div>
                    <div className="form-group half-width">
                      <label>{t('geofencing.longitude')}</label>
                      <input
                        type="number"
                        step="any"
                        value={location.coordinates.lng}
                        onChange={(e) => handleDeliveryLocationChange(index, 'lng', e.target.value)}
                        className="form-input"
                        required
                      />
                    </div>
                  </div>
                </div>
              ))
            )}
            {locationErrors.dropoff && (
              <span className="error-message">{locationErrors.dropoff}</span>
            )}
          </section>
          
          {/* Loads Section */}
          <section className="form-section">
            <div className="section-header-with-button">
              <h2>{t('po.loads')}</h2>
              <button 
                type="button" 
                className="add-button" 
                onClick={addLoad}
              >
                <PlusCircle size={16} />
                {t('po.addLoad')}
              </button>
            </div>
            
            {loads.length === 0 ? (
              <div className="empty-state">
                {t('po.noLoads')}
              </div>
            ) : (
              loads.map((load: Load, index: number) => (
                <div key={index} className="load-item">
                  <div className="load-header">
                    <h3>{t('po.load')} #{index + 1}</h3>
                    <button 
                      type="button" 
                      className="remove-button" 
                      onClick={() => removeLoad(index)}
                      disabled={loads.length <= 1}
                    >
                      <Trash2 size={14} />
                      {t('common.remove')}
                    </button>
                  </div>
                  
                  <div className="form-row">
                    <div className="form-group">
                      <label>{t('ticket.materialCategory')}</label>
                      <select
                        value={load.category || ''}
                        onChange={(e) => {
                          handleLoadChange(index, 'category', e.target.value);
                          // Reset material type when category changes
                          if (load.category !== e.target.value) {
                            handleLoadChange(index, 'materialType', '');
                          }
                        }}
                        className="form-input"
                        required
                      >
                        <option value="">{t('po.selectCategory')}</option>
                        {Object.keys(materialTypeOptions).map((category) => (
                          <option key={category} value={category}>{category}</option>
                        ))}
                      </select>
                    </div>
                    
                    <div className="form-group">
                      <label>{t('ticket.materialType')}</label>
                      <select
                        value={load.materialType}
                        onChange={(e) => handleLoadChange(index, 'materialType', e.target.value)}
                        className="form-input"
                        required
                        disabled={!load.category}
                      >
                        <option value="">{t('po.selectMaterialType')}</option>
                        {load.category && materialTypeOptions[load.category as keyof typeof materialTypeOptions]?.map((material) => (
                          <option key={material} value={material}>{material}</option>
                        ))}
                      </select>
                    </div>
                    
                    <div className="form-group">
                      <label>{t('po.quantity')}</label>
                      <input
                        type="number"
                        placeholder={t('po.enterQuantity')}
                        value={load.quantity}
                        onChange={(e) => handleLoadChange(index, 'quantity', e.target.value)}
                        className="form-input"
                        required
                      />
                    </div>
                    
                    <div className="form-group">
                      <label>{t('ticket.unit')}</label>
                      <select
                        value={load.unit}
                        onChange={(e) => handleLoadChange(index, 'unit', e.target.value)}
                        className="form-input"
                        required
                      >
                        <option value="tons">{t('units.tons')}</option>
                        <option value="cubic_yards">{t('units.cubicYards')}</option>
                        <option value="cubic_meters">{t('units.cubicMeters')}</option>
                        <option value="kilograms">{t('units.kilograms')}</option>
                      </select>
                    </div>
                  </div>
                </div>
              ))
            )}
          </section>
          
          <div className="form-actions">
            <button 
              type="button" 
              className="cancel-button" 
              onClick={() => navigate('/purchase-orders')}
              disabled={loading}
            >
              {t('common.cancel')}
            </button>
            <button 
              type="submit" 
              className="submit-button" 
              disabled={loading}
            >
              {loading ? t('common.saving') : t('common.save')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Add the styles to the document
if (typeof document !== 'undefined' && !document.getElementById('purchase-order-form-additional-styles')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'purchase-order-form-additional-styles';
  styleElement.textContent = additionalStyles;
  document.head.appendChild(styleElement);
}

export default PurchaseOrderForm;
