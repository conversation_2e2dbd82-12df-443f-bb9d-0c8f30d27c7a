/* Global Styles for MaterialFlow */

:root {
  /* Common color variables to ensure consistency */
  --primary: #3b82f6;
  --primary-hover: #2563eb;
  --primary-foreground: #ffffff;
  
  --secondary: #6b7280;
  --secondary-hover: #4b5563;
  
  --accent: #f3f4f6;
  --accent-foreground: #111827;
  
  --background: #ffffff;
  --foreground: #ffffff;
  
  --muted: #f3f4f6;
  --muted-foreground: #6b7280;
  
  --border: #e5e7eb;
  --input: #e5e7eb;
  
  --card: #ffffff;
  --card-foreground: #111827;
  
  --destructive: #ef4444;
  --destructive-hover: #dc2626;
  
  --success: #10b981;
  --warning: #f59e0b;
  --info: #3b82f6;
}

/* Typography */
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON>l, sans-serif;
  color: var(--foreground);
  background-color: #1e1e1e;
  line-height: 1.5;
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 600;
  line-height: 1.2;
  color: var(--foreground);
}

h1 { font-size: 2rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }

p {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--foreground);
}

/* For better contrast in forms and cards */
.card, .form-group input, .form-group select, .form-group textarea {
  color: #111827; /* Dark text for input elements */
}

.table th {
  color: var(--foreground);
}

.table td {
  color: #111827; /* Better contrast for table cells */
}

/* Labels should be white */
label {
  color: var(--foreground);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.btn-primary {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--secondary);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--secondary-hover);
}

.btn-outline {
  background-color: transparent;
  border-color: var(--border);
  color: var(--foreground);
}

.btn-outline:hover {
  background-color: var(--accent);
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.btn-danger {
  background-color: var(--destructive);
  color: white;
}

.btn-danger:hover {
  background-color: var(--destructive-hover);
}

/* Forms */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--foreground);
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="number"],
.form-group input[type="date"],
.form-group input[type="url"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--input);
  border-radius: 0.375rem;
  background-color: var(--background);
  color: #111827;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.form-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

/* Cards */
.card {
  background-color: var(--card);
  border-radius: 0.5rem;
  border: 1px solid var(--border);
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.card-title, .card-subtitle {
  color: #111827;
}

/* Tables - MTO Flagship Style */
.table-container {
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  overflow: hidden;
  margin-top: 1rem;
  background-color: var(--background);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background-color: #2d3748;
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--foreground);
  border-bottom: 1px solid var(--border);
  border-right: 1px solid var(--border);
}

.table th:last-child {
  border-right: none;
}

.table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border);
  border-right: 1px solid var(--border);
  vertical-align: middle;
  background-color: #ffffff;
}

.table td:last-child {
  border-right: none;
}

.table tr:nth-child(even) td {
  background-color: var(--muted);
}

/* Status badges */
.status {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status.active {
  background-color: var(--success);
  color: white;
}

.status.pending {
  background-color: var(--warning);
  color: white;
}

.status.inactive {
  background-color: var(--secondary);
  color: white;
}

/* Section styling */
.section {
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

/* Filters section */
.filters-section {
  margin-bottom: 1.5rem;
}

.filters-section h3 {
  margin-bottom: 1rem;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

/* Navigation and menu items should be white */
nav a, .menu-item, .navigation-link {
  color: var(--foreground);
}

/* Make sure buttons have proper contrast */
button, .button {
  color: inherit;
}

/* Make sure lists and other text elements are white by default */
li, span:not(.badge):not(.status), div:not(.card):not(.form-group):not(.table-container) {
  color: var(--foreground);
}

/* Ensure that input placeholders have sufficient contrast */
::placeholder {
  color: #9ca3af;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filters-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}