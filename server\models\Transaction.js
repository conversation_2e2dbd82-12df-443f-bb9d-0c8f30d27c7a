const mongoose = require('mongoose');

// Define transaction status enum
const TRANSACTION_STATUS = {
  CREATED: 'created',           // Transaction created but not started
  PICKUP_STARTED: 'pickup_started',   // Driver has started the pickup process
  PICKUP_COMPLETED: 'pickup_completed', // Material picked up, awaiting dump
  DUMP_STARTED: 'dump_started',     // Driver has started the dump process
  COMPLETED: 'completed'        // Full cycle completed (pickup and dump)
};

// Schema for capturing verification data at both pickup and dump locations
const verificationSchema = new mongoose.Schema({
  photoUrl: {
    type: String,
    required: true
  },
  geolocation: {
    lat: {
      type: Number,
      required: true
    },
    lng: {
      type: Number,
      required: true
    }
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  signature: {
    name: {
      type: String,
      required: true
    },
    signatureImageUrl: {
      type: String,
      required: true
    }
  },
  weighTicketNumber: String,
  weight: Number,
  notes: String
}, { _id: false });

// Main transaction schema
const transactionSchema = new mongoose.Schema({
  purchaseOrderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'PurchaseOrder',
    required: true
  },
  truckerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  materialType: {
    type: String,
    required: true
  },
  quantity: {
    type: Number,
    default: 0 // Will be updated after dump is completed with actual weight
  },
  unit: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: Object.values(TRANSACTION_STATUS),
    default: TRANSACTION_STATUS.CREATED
  },
  // Pickup details
  pickup: {
    location: {
      type: {
        name: String,
        address: String,
        coordinates: {
          lat: Number,
          lng: Number
        }
      },
      required: true
    },
    verification: verificationSchema
  },
  // Dump details
  dump: {
    location: {
      type: {
        name: String,
        address: String,
        coordinates: {
          lat: Number,
          lng: Number
        }
      },
      required: true
    },
    verification: verificationSchema
  },
  // Calculated charges after dump is completed
  charges: {
    haulerRate: {
      type: Number,
      default: 0
    },
    resaleRate: {
      type: Number,
      default: 0
    },
    totalHaulerCharge: {
      type: Number,
      default: 0
    },
    totalResaleCharge: {
      type: Number,
      default: 0
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt timestamp before saving
transactionSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Method to update transaction status
transactionSchema.methods.updateStatus = function(newStatus) {
  if (Object.values(TRANSACTION_STATUS).includes(newStatus)) {
    this.status = newStatus;
    return true;
  }
  return false;
};

// Method to calculate charges after dump is completed
transactionSchema.methods.calculateCharges = function(haulerRate, resaleRate) {
  if (this.status !== TRANSACTION_STATUS.COMPLETED) {
    return false;
  }
  
  // Calculate charges based on actual quantity and rates
  this.charges.haulerRate = haulerRate;
  this.charges.resaleRate = resaleRate;
  this.charges.totalHaulerCharge = this.quantity * haulerRate;
  this.charges.totalResaleCharge = this.quantity * resaleRate;
  
  return true;
};

// Export the model and the status enum
module.exports = {
  Transaction: mongoose.model('Transaction', transactionSchema),
  TRANSACTION_STATUS
};
