{"common": {"submit": "Enviar", "submitting": "Enviando...", "add": "Agregar", "update": "Actualizar", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>"}, "ticket": {"create": "<PERSON><PERSON><PERSON> Ticket", "edit": "<PERSON><PERSON>", "pickupLocation": "Ubicación de Recogida", "dropoffLocation": "Ubicación de Entrega", "materialType": "Tipo de Material", "quantity": "Cantidad", "unit": "Unidad", "notes": "Notas"}, "tickets": {"title": "Boleto de Material", "backup": "Copia de Respaldo <PERSON>", "print": "<PERSON><PERSON><PERSON><PERSON>", "ticketNumber": "Número de Boleto", "poNumber": "Orden de Compra", "materialType": "Tipo de Material", "quantity": "Cantidad", "date": "<PERSON><PERSON> y <PERSON>", "location": "Ubicación", "driverSignature": "Firma del Conductor", "status": {"open": "<PERSON>bie<PERSON>o", "completed": "Completado", "pending": "<PERSON>bie<PERSON>o", "approved": "<PERSON>bie<PERSON>o", "rejected": "<PERSON>bie<PERSON>o", "expired": "<PERSON>bie<PERSON>o", "in_progress": "<PERSON>bie<PERSON>o", "available": "<PERSON>bie<PERSON>o", "accepted": "<PERSON>bie<PERSON>o"}}, "navigation": {"administration": "Administración", "adminDesc": "Administrar configuración del sistema y cuentas de usuario", "materialTicketing": "Boletos de Material", "ticketingDesc": "<PERSON><PERSON>, administrar y procesar boletos de material", "purchaseOrders": "<PERSON><PERSON><PERSON> de Compra", "poDesc": "<PERSON><PERSON>r y administrar órdenes de compra", "mtoFlagship": "MTO Flagship", "mtoDesc": "Acceder a características y herramientas principales", "ticketArchive": "Archivo de Boletos", "archiveDesc": "Ver y buscar boletos archivados"}, "po": {"create": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "approve": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON>", "status": "Estado", "haulerRates": "Tarifas del Transportista", "resaleRates": "<PERSON><PERSON><PERSON><PERSON> de Reventa", "clientApproval": "Aprobación del Cliente", "location": "Ubicación"}, "trucker": {"noActiveJob": "Sin Trabajo Activo", "browseAvailableJobs": "Explora los trabajos disponibles para comenzar.", "viewAvailableJobs": "Ver Trabajos Disponibles", "startRoute": "<PERSON><PERSON><PERSON>", "enRouteToJobsite": "En ruta a la obra", "markAsArrived": "Marcar como Llegado", "startJob": "Comenzar <PERSON>aj<PERSON>", "completeJob": "Completar Trabajo", "jobGeofence": "Geocerca del Trabajo", "newTransaction": "Nueva Transacción", "activeJob": "Trabajo Activo"}, "ui": {"materialTicketHistory": "Historial de Tickets de Material", "loadingTickets": "Cargando tickets...", "devMode": "<PERSON><PERSON>", "testLogin": "<PERSON><PERSON>", "poLogin": "Login PO", "truckerLogin": "Login Transportista", "toggleDevMode": "Alternar Modo <PERSON>", "materialticketing": "Gestión de Tickets de Material", "materialticketingdesc": "Sube, gestiona y procesa tickets de material", "administration": "Administración", "administrationdesc": "Gestiona la configuración del sistema y las cuentas de usuario", "purchaseorders": "<PERSON><PERSON><PERSON> de Compra", "purchaseordersdesc": "Gestiona y revisa las órdenes de compra", "clients": "Clientes", "clientsdesc": "Ver y gestionar cuentas de clientes"}, "geofencing": {"manageGeofences": "Gestionar Geocercas", "activeGeofences": "Geocercas Activas", "type": "Tipo", "latitude": "Latitud", "longitude": "<PERSON><PERSON><PERSON>", "radius": "Radio (metros)", "pickupGeofence": "Geocerca de Recogida", "dropoffGeofence": "Geocerca de Entrega", "locationRequired": "La ubicación debe estar dentro de la geocerca", "locationValidation": "Validación de Ubicación", "currentLocation": "Ubicación Actual", "distanceToGeofence": "Distancia a la Geocerca", "geofenceStatus": "Estado de la Geocerca", "withinRange": "Dentro del Rango", "outOfRange": "Fuera del Rango"}}