const { Transaction, TRANSACTION_STATUS } = require('../models/Transaction');
const PurchaseOrder = require('../models/PurchaseOrder');
const mongoose = require('mongoose');
const turf = require('@turf/turf');

// Helper function to check if a location is within a geofence
const isWithinGeofence = (location, geofence) => {
  if (!location || !geofence) return false;
  
  const point = turf.point([location.lng, location.lat]);
  
  if (geofence.type === 'Polygon') {
    const polygon = turf.polygon([geofence.coordinates]);
    return turf.booleanPointInPolygon(point, polygon);
  } else if (geofence.type === 'Circle') {
    const center = turf.point(geofence.center);
    const distance = turf.distance(point, center, { units: 'meters' });
    return distance <= geofence.radius;
  }
  
  return false;
};

// Get all available purchase orders for a trucker based on their current location
exports.getAvailablePurchaseOrders = async (req, res) => {
  try {
    const { lat, lng } = req.query;
    const truckerId = req.user.id;
    
    if (!lat || !lng) {
      return res.status(400).json({ message: 'Current location is required' });
    }
    
    // Find all open purchase orders
    const purchaseOrders = await PurchaseOrder.find({
      status: 'open',
      assignedTrucker: { $in: [null, truckerId] } // Unassigned or assigned to this trucker
    });
    
    // Filter POs by geofence
    const availablePOs = purchaseOrders.filter(po => {
      return isWithinGeofence({ lat: parseFloat(lat), lng: parseFloat(lng) }, po.geofence);
    });
    
    return res.status(200).json(availablePOs);
  } catch (error) {
    console.error('Error getting available purchase orders:', error);
    return res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Start a new transaction (pickup process)
exports.startPickup = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    const { purchaseOrderId, materialType, locationId } = req.body;
    const truckerId = req.user.id;
    
    // Validate request
    if (!purchaseOrderId || !materialType || !locationId) {
      return res.status(400).json({ message: 'Purchase order ID, material type, and location ID are required' });
    }
    
    // Find the purchase order
    const purchaseOrder = await PurchaseOrder.findById(purchaseOrderId);
    
    if (!purchaseOrder) {
      return res.status(404).json({ message: 'Purchase order not found' });
    }
    
    if (purchaseOrder.status !== 'open') {
      return res.status(400).json({ message: 'Purchase order is not open for transactions' });
    }
    
    // Find the pickup location from the PO
    const pickupLocation = purchaseOrder.startingLocation;
    
    if (!pickupLocation) {
      return res.status(400).json({ message: 'Pickup location not found in purchase order' });
    }
    
    // Find the dump location from the PO's delivery locations
    const dumpLocation = purchaseOrder.deliveryLocations.find(
      location => location._id.toString() === locationId
    );
    
    if (!dumpLocation) {
      return res.status(400).json({ message: 'Dump location not found in purchase order' });
    }
    
    // Find the load details for the material type
    const load = purchaseOrder.loads.find(load => load.materialType === materialType);
    
    if (!load) {
      return res.status(400).json({ message: 'Material type not found in purchase order' });
    }
    
    // Create a new transaction
    const transaction = new Transaction({
      purchaseOrderId,
      truckerId,
      materialType: load.materialType,
      unit: load.unit,
      status: TRANSACTION_STATUS.CREATED,
      pickup: {
        location: pickupLocation
      },
      dump: {
        location: dumpLocation
      }
    });
    
    // Update transaction status to PICKUP_STARTED
    transaction.status = TRANSACTION_STATUS.PICKUP_STARTED;
    
    // Save the transaction
    await transaction.save({ session });
    
    // Update purchase order status to in_progress if not already
    if (purchaseOrder.status !== 'in_progress') {
      purchaseOrder.status = 'in_progress';
      purchaseOrder.assignedTrucker = truckerId;
      await purchaseOrder.save({ session });
    }
    
    await session.commitTransaction();
    session.endSession();
    
    return res.status(201).json({
      message: 'Pickup started successfully',
      transaction
    });
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    
    console.error('Error starting pickup:', error);
    return res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Complete pickup process
exports.completePickup = async (req, res) => {
  try {
    const { transactionId } = req.params;
    const { 
      photoUrl, 
      lat, 
      lng, 
      signerName, 
      signatureImageUrl,
      weighTicketNumber,
      weight,
      notes
    } = req.body;
    
    // Validate request
    if (!photoUrl || !lat || !lng || !signerName || !signatureImageUrl) {
      return res.status(400).json({ 
        message: 'Photo, geolocation, signer name, and signature are required' 
      });
    }
    
    // Find the transaction
    const transaction = await Transaction.findById(transactionId);
    
    if (!transaction) {
      return res.status(404).json({ message: 'Transaction not found' });
    }
    
    if (transaction.status !== TRANSACTION_STATUS.PICKUP_STARTED) {
      return res.status(400).json({ 
        message: 'Transaction is not in pickup started state' 
      });
    }
    
    // Update transaction with pickup verification data
    transaction.pickup.verification = {
      photoUrl,
      geolocation: { lat, lng },
      timestamp: new Date(),
      signature: {
        name: signerName,
        signatureImageUrl
      },
      weighTicketNumber,
      weight,
      notes
    };
    
    // Update transaction status
    transaction.status = TRANSACTION_STATUS.PICKUP_COMPLETED;
    
    // Save the transaction
    await transaction.save();
    
    return res.status(200).json({
      message: 'Pickup completed successfully',
      transaction
    });
  } catch (error) {
    console.error('Error completing pickup:', error);
    return res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Start dump process
exports.startDump = async (req, res) => {
  try {
    const { transactionId } = req.params;
    
    // Find the transaction
    const transaction = await Transaction.findById(transactionId);
    
    if (!transaction) {
      return res.status(404).json({ message: 'Transaction not found' });
    }
    
    if (transaction.status !== TRANSACTION_STATUS.PICKUP_COMPLETED) {
      return res.status(400).json({ 
        message: 'Transaction must be in pickup completed state to start dump' 
      });
    }
    
    // Update transaction status
    transaction.status = TRANSACTION_STATUS.DUMP_STARTED;
    
    // Save the transaction
    await transaction.save();
    
    return res.status(200).json({
      message: 'Dump process started successfully',
      transaction
    });
  } catch (error) {
    console.error('Error starting dump process:', error);
    return res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Complete dump process and the full transaction cycle
exports.completeDump = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    const { transactionId } = req.params;
    const { 
      photoUrl, 
      lat, 
      lng, 
      signerName, 
      signatureImageUrl,
      weighTicketNumber,
      weight,
      notes
    } = req.body;
    
    // Validate request
    if (!photoUrl || !lat || !lng || !signerName || !signatureImageUrl || !weighTicketNumber || !weight) {
      return res.status(400).json({ 
        message: 'Photo, geolocation, signer name, signature, weigh ticket number, and weight are required' 
      });
    }
    
    // Find the transaction
    const transaction = await Transaction.findById(transactionId);
    
    if (!transaction) {
      return res.status(404).json({ message: 'Transaction not found' });
    }
    
    if (transaction.status !== TRANSACTION_STATUS.DUMP_STARTED) {
      return res.status(400).json({ 
        message: 'Transaction must be in dump started state to complete' 
      });
    }
    
    // Update transaction with dump verification data
    transaction.dump.verification = {
      photoUrl,
      geolocation: { lat, lng },
      timestamp: new Date(),
      signature: {
        name: signerName,
        signatureImageUrl
      },
      weighTicketNumber,
      weight,
      notes
    };
    
    // Update transaction quantity with actual weight
    transaction.quantity = weight;
    
    // Update transaction status
    transaction.status = TRANSACTION_STATUS.COMPLETED;
    
    // Find the purchase order to get rates
    const purchaseOrder = await PurchaseOrder.findById(transaction.purchaseOrderId);
    
    if (!purchaseOrder) {
      return res.status(404).json({ message: 'Purchase order not found' });
    }
    
    // Find the rates for this material type
    const haulerRate = purchaseOrder.haulerRates.find(
      rate => rate.materialType === transaction.materialType
    );
    
    const resaleRate = purchaseOrder.resaleRates.find(
      rate => rate.materialType === transaction.materialType
    );
    
    if (!haulerRate || !resaleRate) {
      return res.status(400).json({ message: 'Rates not found for this material type' });
    }
    
    // Calculate charges
    transaction.calculateCharges(haulerRate.rate, resaleRate.rate);
    
    // Save the transaction
    await transaction.save({ session });
    
    // Add the transaction to the purchase order's tickets array
    purchaseOrder.tickets.push(transaction._id);
    await purchaseOrder.save({ session });
    
    await session.commitTransaction();
    session.endSession();
    
    return res.status(200).json({
      message: 'Dump completed successfully and charges calculated',
      transaction
    });
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    
    console.error('Error completing dump:', error);
    return res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get all transactions for a purchase order
exports.getTransactionsByPurchaseOrder = async (req, res) => {
  try {
    const { purchaseOrderId } = req.params;
    
    const transactions = await Transaction.find({ purchaseOrderId });
    
    return res.status(200).json(transactions);
  } catch (error) {
    console.error('Error getting transactions:', error);
    return res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get all open transactions for a trucker
exports.getTruckerOpenTransactions = async (req, res) => {
  try {
    const truckerId = req.user.id;
    
    const transactions = await Transaction.find({
      truckerId,
      status: { 
        $in: [
          TRANSACTION_STATUS.PICKUP_STARTED, 
          TRANSACTION_STATUS.PICKUP_COMPLETED,
          TRANSACTION_STATUS.DUMP_STARTED
        ] 
      }
    }).populate('purchaseOrderId');
    
    return res.status(200).json(transactions);
  } catch (error) {
    console.error('Error getting trucker transactions:', error);
    return res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Admin endpoint to manually close a purchase order
exports.closePurchaseOrder = async (req, res) => {
  try {
    const { purchaseOrderId } = req.params;
    const adminId = req.user.id;
    
    // Verify user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Only administrators can close purchase orders' });
    }
    
    const purchaseOrder = await PurchaseOrder.findById(purchaseOrderId);
    
    if (!purchaseOrder) {
      return res.status(404).json({ message: 'Purchase order not found' });
    }
    
    // Update purchase order status to completed
    purchaseOrder.status = 'completed';
    purchaseOrder.approvedAt = new Date();
    purchaseOrder.approvedBy = adminId;
    
    await purchaseOrder.save();
    
    return res.status(200).json({
      message: 'Purchase order closed successfully',
      purchaseOrder
    });
  } catch (error) {
    console.error('Error closing purchase order:', error);
    return res.status(500).json({ message: 'Server error', error: error.message });
  }
};
