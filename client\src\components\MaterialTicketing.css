.material-ticketing {
  min-height: 100vh;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  background: #f5f5f5;
  color: #333;
}

/* Reset any inherited white text from home page */
.material-ticketing * {
  color: inherit;
}

.material-ticketing h1,
.material-ticketing h2,
.material-ticketing h3,
.material-ticketing h4,
.material-ticketing h5,
.material-ticketing h6 {
  color: #333;
}

.material-ticketing p,
.material-ticketing span,
.material-ticketing div {
  color: #333;
}

.material-ticketing label {
  color: #333;
}

.material-ticketing input,
.material-ticketing select,
.material-ticketing textarea {
  color: #333;
}

.page-header {
  background: linear-gradient(135deg, #1976D2 0%, #1976D2 30%, #1565C0 50%, #0D47A1 70%, #FF8F00 85%, #FFA000 100%);
  color: white;
  padding: 2rem;
}

.page-header * {
  color: white !important;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.page-brand {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-top: 1rem;
}

.page-logo {
  max-height: 60px;
  max-width: 150px;
  object-fit: contain;
  filter: drop-shadow(1px 1px 3px rgba(0, 0, 0, 0.3));
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 6px;
}

.page-title {
  flex: 1;
}

.back-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.page-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.page-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto 1fr;
  gap: 2rem;
  grid-template-areas:
    "upload actions"
    "tickets tickets";
}

.upload-section {
  grid-area: upload;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.actions-section {
  grid-area: actions;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tickets-section {
  grid-area: tickets;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.content-grid h2 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid #2196F3;
  padding-bottom: 0.5rem;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.archive-button {
  background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
  color: white;
  border: none;
  padding: 0.875rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.archive-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3);
}

.reporting-button {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
  color: white;
  border: none;
  padding: 0.875rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.reporting-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
}

.reporting-section {
  margin-top: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.selection-info {
  color: #666;
  font-size: 0.9rem;
  font-style: italic;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-size: 1.1rem;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-header {
    padding: 1rem;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .page-main {
    padding: 1rem;
  }

  .content-grid {
    grid-template-columns: 1fr;
    grid-template-areas:
      "upload"
      "actions"
      "tickets";
    gap: 1rem;
  }

  .upload-section,
  .actions-section,
  .tickets-section {
    padding: 1.5rem;
  }

  .action-buttons {
    flex-direction: column;
  }
}
