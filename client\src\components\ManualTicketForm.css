/* Mobile-first design */
.manual-ticket-form {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 1.5rem;
  width: 100%;
  max-width: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

/* Signature input integration */
.manual-ticket-form .signature-input {
  margin-top: 0.5rem;
}

.manual-ticket-form .signature-input .signature-label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.manual-ticket-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2196F3, #64B5F6, #2196F3);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: -200% 0; }
}

.manual-ticket-form .form-row {
  display: flex;
  flex-direction: column;
  gap: 0;
  margin-bottom: 0.25rem;
}

.manual-ticket-form .form-group {
  margin-bottom: 0.25rem;
}

.manual-ticket-form label {
  display: block;
  margin-bottom: 0.3rem;
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
  line-height: 1.4;
  position: relative;
}

.manual-ticket-form label::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, #2196F3, #64B5F6);
  border-radius: 1px;
}

/* Mobile-first: Large touch targets with visible borders */
.manual-ticket-form input,
.manual-ticket-form select,
.manual-ticket-form textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #bbb;
  border-radius: 8px;
  font-size: 1rem;
  line-height: 1.5;
  transition: all 0.3s ease;
  box-sizing: border-box;
  min-height: 48px; /* Minimum touch target size */
  background: white;
  color: #333;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.manual-ticket-form input:hover,
.manual-ticket-form select:hover,
.manual-ticket-form textarea:hover {
  border-color: #888;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.manual-ticket-form input:focus,
.manual-ticket-form select:focus,
.manual-ticket-form textarea:focus {
  outline: none;
  border-color: #2196F3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2), 0 2px 8px rgba(0, 0, 0, 0.15);
  background: #fafafa;
}

/* Placeholder styling */
.manual-ticket-form input::placeholder,
.manual-ticket-form textarea::placeholder {
  color: #999;
  font-style: italic;
  opacity: 1;
}

/* Required field indicator */
.manual-ticket-form label[data-required="true"]::before {
  content: '* ';
  color: #e74c3c;
  font-weight: bold;
  margin-right: 2px;
}

/* Invalid field styling */
.manual-ticket-form input:invalid:not(:placeholder-shown),
.manual-ticket-form select:invalid:not(:placeholder-shown),
.manual-ticket-form textarea:invalid:not(:placeholder-shown) {
  border-color: #e74c3c;
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.1);
}

.manual-ticket-form textarea {
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
}

.manual-ticket-form .file-input {
  padding: 1rem;
  border: 2px dashed #999;
  border-radius: 8px;
  background: #f9f9f9;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 48px;
  text-align: center;
  color: #555;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.manual-ticket-form .file-input:hover {
  border-color: #2196F3;
  background: #f0f8ff;
  color: #2196F3;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.manual-ticket-form .file-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #f0f8ff;
  border-radius: 6px;
  border: 1px solid #2196F3;
}

.manual-ticket-form .file-name {
  font-weight: 500;
  color: #1976D2;
}

.manual-ticket-form .file-size {
  font-size: 0.85rem;
  color: #666;
}

.submit-button {
  width: 100%;
  padding: 1rem 1.5rem;
  background: #2196F3;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 48px;
  margin-top: 0.5rem;
}

.submit-button:hover:not(:disabled) {
  background: #1976D2;
  transform: translateY(-1px);
}

.submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.message {
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
}

.message.success {
  background: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.message.error {
  background: #ffebee;
  color: #c62828;
  border: 1px solid #ffcdd2;
}

/* Tablet and larger screens */
@media (min-width: 600px) {
  .manual-ticket-form {
    padding: 1.5rem;
    max-width: 600px;
    margin: 0 auto;
  }

  .manual-ticket-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 0.4rem;
  }

  .manual-ticket-form .form-group {
    margin-bottom: 0.4rem;
  }

  .manual-ticket-form label {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
  }

  .manual-ticket-form input,
  .manual-ticket-form select,
  .manual-ticket-form textarea {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .submit-button {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}

/* Desktop screens */
@media (min-width: 1024px) {
  .manual-ticket-form {
    padding: 2rem;
  }
}
