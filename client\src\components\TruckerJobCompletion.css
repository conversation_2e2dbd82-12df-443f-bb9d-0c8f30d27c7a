.job-completion-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.job-completion-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  overflow: hidden;
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.job-header {
  padding: 1.5rem;
  background: #4CAF50;
  color: white;
}

.job-header h2 {
  margin: 0 0 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.job-title {
  font-size: 1.2rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.client-info {
  font-size: 0.9rem;
  opacity: 0.9;
}

.completion-form {
  padding: 1.5rem;
}

.form-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.form-section:last-child {
  border-bottom: none;
}

.form-section h3 {
  margin: 0 0 1.25rem;
  font-size: 1.1rem;
  color: #333;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #555;
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: #4CAF50;
  outline: none;
}

.file-help-text {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.5rem;
}

.cans-container {
  margin-bottom: 1rem;
}

.can-item {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.can-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.can-number {
  font-weight: 500;
  color: #333;
}

.remove-can-btn {
  background: none;
  border: none;
  color: #f44336;
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.remove-can-btn:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

.can-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.add-can-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #f5f5f5;
  border: 1px dashed #ccc;
  border-radius: 6px;
  color: #4CAF50;
  font-size: 0.9rem;
  cursor: pointer;
  width: 100%;
  justify-content: center;
  transition: all 0.3s ease;
}

.add-can-btn:hover {
  background: #e8f5e9;
  border-color: #4CAF50;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 6px;
  color: #c62828;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.primary-button,
.secondary-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.primary-button {
  background: #4CAF50;
  color: white;
}

.primary-button:hover {
  background: #388E3C;
}

.primary-button:disabled {
  background: #A5D6A7;
  cursor: not-allowed;
}

.secondary-button {
  background: #f5f5f5;
  color: #333;
}

.secondary-button:hover {
  background: #e0e0e0;
}

.secondary-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  width: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.error-container {
  text-align: center;
  padding: 3rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.error-container h2 {
  color: #c62828;
  margin-bottom: 1rem;
}

.error-container p {
  color: #666;
  margin-bottom: 2rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .job-completion-container {
    padding: 1rem;
  }
  
  .can-inputs {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .primary-button,
  .secondary-button {
    width: 100%;
  }
}
