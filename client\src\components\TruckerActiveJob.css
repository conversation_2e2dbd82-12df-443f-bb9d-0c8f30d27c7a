.active-job-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-height: 100vh;
  background: #f5f7fa;
}

.active-job-header {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-content {
  width: 100%;
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-content h1 {
  margin: 0 0 0.5rem;
  font-size: 1.5rem;
  color: #333;
}

.job-title {
  font-size: 1.2rem;
  font-weight: 500;
  color: #1E88E5;
  margin-bottom: 0.75rem;
}

.status-section {
  margin-top: 0.5rem;
}

.status-badge {
  display: inline-block;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-accepted {
  background-color: #E3F2FD;
  color: #1565C0;
}

.status-en-route {
  background-color: #E8F5E9;
  color: #2E7D32;
}

.status-on-site {
  background-color: #FFF3E0;
  color: #E65100;
}

.status-in-progress {
  background-color: #FFEBEE;
  color: #C62828;
}

.status-completed {
  background-color: #E8EAF6;
  color: #3949AB;
}

.status-verified {
  background-color: #E0F2F1;
  color: #00695C;
}

.logout-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f5f5f5;
  border: none;
  border-radius: 8px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-button:hover {
  background: #e0e0e0;
  color: #333;
}

.active-job-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.job-details-card,
.map-section,
.action-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
}

.job-details-card h2,
.map-section h2 {
  margin: 0 0 1.25rem;
  font-size: 1.2rem;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.75rem;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.25rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

.detail-value {
  font-size: 1rem;
  color: #333;
}

.map-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 1rem;
}

.geofence-status {
  position: absolute;
  bottom: 10px;
  right: 10px;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
  z-index: 1000;
}

.geofence-status.inside {
  background-color: rgba(46, 125, 50, 0.9);
  color: white;
}

.geofence-status.outside {
  background-color: rgba(198, 40, 40, 0.9);
  color: white;
}

.action-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 6px;
  color: #c62828;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  width: 100%;
}

.status-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 1rem 0;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 300px;
}

.start-route-btn {
  background: #1E88E5;
  color: white;
}

.start-route-btn:hover {
  background: #1565C0;
}

.arrived-btn {
  background: #FF9800;
  color: white;
  margin-top: 1rem;
}

.arrived-btn:hover {
  background: #F57C00;
}

.start-job-btn {
  background: #4CAF50;
  color: white;
}

.start-job-btn:hover {
  background: #388E3C;
}

.complete-job-btn {
  background: #9C27B0;
  color: white;
}

.complete-job-btn:hover {
  background: #7B1FA2;
}

.status-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: #f5f5f5;
  border-radius: 8px;
  color: #333;
  font-size: 1rem;
  margin-bottom: 1rem;
  width: 100%;
  max-width: 500px;
  text-align: center;
  justify-content: center;
  flex-wrap: wrap;
}

.status-message.en-route {
  background: #E8F5E9;
  color: #2E7D32;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1E88E5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.no-job-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 2rem;
  text-align: center;
}

.no-job-message h2 {
  margin: 0 0 1rem;
  color: #333;
}

.no-job-message p {
  margin: 0 0 2rem;
  color: #666;
}

.button-group {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  flex-wrap: wrap;
}

.primary-button,
.secondary-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.6rem 1.2rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  gap: 0.5rem;
  white-space: nowrap;
}

.primary-button {
  background-color: #1E88E5;
  color: white;
  border: 1px solid #1E88E5;
}

.primary-button:hover {
  background-color: #1976D2;
  border-color: #1976D2;
}

.secondary-button {
  background-color: white;
  color: #1E88E5;
  border: 1px solid #1E88E5;
}

.secondary-button:hover {
  background-color: #f5f9ff;
}

.icon {
  width: 1.1rem;
  height: 1.1rem;
  flex-shrink: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .active-job-container {
    padding: 1rem;
  }
  
  .active-job-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .logout-button {
    align-self: flex-end;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .action-button {
    width: 100%;
    max-width: 100%;
  }
}
