.trucker-stats-container {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #2196F3 0%, #2196F3 30%, #1976D2 50%, #0D47A1 70%, #FF8F00 85%, #FFA000 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  color: white;
  position: relative;
}

.trucker-stats-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.15"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.15"/><circle cx="80" cy="30" r="0.5" fill="white" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
  animation: float 20s ease-in-out infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

.loading-spinner, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  text-align: center;
  position: relative;
  z-index: 1;
}

.spinner-large {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container svg {
  color: #ff6b6b;
  margin-bottom: 1rem;
}

.retry-button {
  background: linear-gradient(135deg, #FFA000 0%, #FFB74D 100%);
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 160, 0, 0.4);
}

.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem;
  position: relative;
  z-index: 1;
  animation: slideInDown 0.8s ease-out;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateX(-2px);
}

.driver-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.driver-icon {
  color: #FFA000;
  filter: drop-shadow(0 0 8px rgba(255, 160, 0, 0.3));
}

.driver-info h1 {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.driver-info p {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

.logout-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #495057;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logout-button:hover {
  background-color: #e9ecef;
  border-color: #ced4da;
}

.material-ticketing-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #007bff;
  border: 1px solid #0056b3;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 12px;
}

.material-ticketing-button:hover {
  background-color: #0056b3;
  border-color: #004085;
}

.material-ticketing-button svg {
  stroke: white;
}

.stats-main {
  padding: 0 2rem 2rem;
  position: relative;
  z-index: 1;
  animation: fadeInUp 0.8s ease-out 0.3s both;
}

/* Mobile-first: Single column layout */
.stats-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

/* Mobile-first: Compact stat cards */
.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  animation: slideInCard 0.8s ease-out both;
  min-height: 80px;
}

.stat-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.stat-card.primary {
  border-left: 4px solid #FFA000;
}

.stat-card.secondary {
  border-left: 4px solid #2196F3;
}

.stat-card.tertiary {
  border-left: 4px solid #4CAF50;
}

.stat-card.quaternary {
  border-left: 4px solid #9C27B0;
}

/* Mobile-first: Smaller icons */
.stat-icon {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 48px;
  height: 48px;
}

.stat-card.primary .stat-icon {
  color: #FFA000;
}

.stat-card.secondary .stat-icon {
  color: #2196F3;
}

.stat-card.tertiary .stat-icon {
  color: #4CAF50;
}

.stat-card.quaternary .stat-icon {
  color: #9C27B0;
}

/* Mobile-first: Smaller text */
.stat-content h3 {
  font-size: 0.9rem;
  font-weight: 500;
  margin: 0 0 0.25rem 0;
  opacity: 0.9;
  line-height: 1.2;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1;
}

.stat-label {
  font-size: 0.8rem;
  opacity: 0.8;
  line-height: 1.2;
}

.recent-loads {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  animation: slideInCard 0.8s ease-out 0.6s both;
}

.recent-loads h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.loads-table {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Mobile-first: Stacked table layout */
.table-header, .table-row {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 12px;
  align-items: stretch;
}

.table-header {
  background: rgba(255, 255, 255, 0.1);
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Mobile: Card-style table rows */
.table-row {
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.table-row:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* Mobile: Add labels for each data point */
.table-row span {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.9rem;
}

.table-row span:last-child {
  border-bottom: none;
}

.table-row span::before {
  content: attr(data-label);
  font-weight: 600;
  opacity: 0.8;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.9);
}

/* Hide table header on mobile */
.table-header {
  display: none;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  opacity: 0.7;
}

.no-data svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInCard {
  from {
    opacity: 0;
    transform: translateY(50px) rotateX(20deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) rotateX(0deg);
  }
}

/* Tablet and larger screens */
@media (min-width: 600px) {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
  }

  .stat-card {
    padding: 2rem;
    gap: 1.5rem;
    border-radius: 20px;
    min-height: auto;
  }

  .stat-icon {
    padding: 1rem;
    border-radius: 16px;
    width: auto;
    height: auto;
  }

  .stat-content h3 {
    font-size: 1rem;
    margin: 0 0 0.5rem 0;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .stat-label {
    font-size: 0.9rem;
  }

  .table-header {
    display: grid;
    grid-template-columns: 1fr 1.5fr 1.5fr 1fr 1fr 1fr;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.1);
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .table-row {
    display: grid;
    grid-template-columns: 1fr 1.5fr 1.5fr 1fr 1fr 1fr;
    gap: 1rem;
    margin-bottom: 0;
    border: none;
    flex-direction: row;
  }

  .table-row span {
    display: block;
    padding: 1rem;
    border-bottom: none;
    justify-content: flex-start;
  }

  .table-row span::before {
    display: none;
  }

  .table-row:hover {
    transform: translateX(5px);
  }
}

/* Desktop screens */
@media (min-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
