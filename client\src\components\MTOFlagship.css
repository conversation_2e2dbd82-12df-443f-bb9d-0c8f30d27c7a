.mto-flagship {
  min-height: 100vh;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  background: #f5f5f5;
  color: #333 !important;
}

/* Ensure all text in MTO flagship is dark */
.mto-flagship * {
  color: inherit !important;
}

.mto-flagship h1,
.mto-flagship h2,
.mto-flagship h3,
.mto-flagship h4,
.mto-flagship h5,
.mto-flagship h6 {
  color: #333 !important;
}

.mto-flagship p,
.mto-flagship span,
.mto-flagship div,
.mto-flagship label,
.mto-flagship td,
.mto-flagship th {
  color: #333 !important;
}

.mto-flagship input,
.mto-flagship select,
.mto-flagship textarea {
  color: #333 !important;
}

.page-header {
  background: linear-gradient(135deg, #1E88E5 0%, #1E88E5 30%, #1565C0 50%, #0D47A1 70%, #FF8F00 85%, #FFA000 100%);
  color: white;
  padding: 2rem;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.page-brand {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-top: 1rem;
}

.page-logo {
  max-height: 60px;
  max-width: 150px;
  object-fit: contain;
  filter: drop-shadow(1px 1px 3px rgba(0, 0, 0, 0.3));
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 6px;
}

.page-title {
  flex: 1;
}

.back-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.page-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.page-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.mto-container {
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 140px);
}

.mto-sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e0e0e0;
  padding: 2rem 0;
}

.mto-sidebar h3 {
  padding: 0 2rem;
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.mto-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 1rem 2rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
  font-size: 0.95rem;
}

.nav-item:hover {
  background: #f8f9fa;
  color: #333;
}

.nav-item.active {
  background: #e3f2fd;
  color: #1976d2;
  border-right: 3px solid #2196f3;
}

.nav-icon {
  font-size: 1.2rem;
  margin-right: 0.75rem;
  width: 24px;
  text-align: center;
}

.nav-text {
  flex: 1;
}

.mto-content {
  flex: 1;
  padding: 2rem;
  background: #fafafa;
}

/* Section Styles */
.section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.section-header h2 {
  color: #333;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
}

/* Button Styles */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.btn-secondary {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.btn-secondary:hover {
  background: #e0e0e0;
  color: #333;
}

.btn-success {
  background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
  color: white;
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-warning {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
  color: white;
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
}

.btn-danger {
  background: #f44336;
  color: white;
}

.btn-danger:hover {
  background: #d32f2f;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
}

/* Form Styles */
.form-container {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  border: 1px solid #e9ecef;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2196F3;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

/* Table Styles */
.table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  margin-bottom: 2rem;
}

.table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.table th,
.table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table tr:hover {
  background: #f8f9fa;
}

.table td {
  color: #555;
}

/* Status Styles */
.status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status.completed {
  background: #e8f5e8;
  color: #2e7d32;
}

.status.pending {
  background: #fff3e0;
  color: #f57c00;
}

.status.in-progress {
  background: #e3f2fd;
  color: #1976d2;
}

.status.cancelled {
  background: #ffebee;
  color: #c62828;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .mto-container {
    flex-direction: column;
  }

  .mto-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }

  .mto-nav {
    display: flex;
    overflow-x: auto;
    padding: 0 1rem;
  }

  .nav-item {
    white-space: nowrap;
    min-width: 180px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 1.5rem;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .mto-content {
    padding: 1rem;
  }

  .section {
    padding: 1.5rem;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .table-container {
    font-size: 0.9rem;
  }

  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
  }
}
