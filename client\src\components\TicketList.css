.ticket-list {
  width: 100%;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: #666;
  font-size: 1.1rem;
}

.list-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.select-all {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #333;
  cursor: pointer;
}

.select-all input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.tickets-grid {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

.ticket-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1.5rem;
  background: #fafafa;
  transition: all 0.3s ease;
}

.ticket-card:hover {
  border-color: #2196F3;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
}

.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.ticket-select input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.status-badge {
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.ticket-content {
  margin-bottom: 1rem;
}

.clickable-content {
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
  border-radius: 8px;
  margin: -8px;
  padding: 24px !important;
  position: relative;
}

.clickable-content:hover {
  background-color: rgba(102, 126, 234, 0.05);
  transform: translateY(-1px);
}

.clickable-content::after {
  content: "👁️ Click to view details";
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.clickable-content:hover::after {
  opacity: 1;
}

.ticket-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  word-break: break-word;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.manual-badge {
  background: #9c27b0;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: uppercase;
}

.ticket-client {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.manual-ticket-details {
  background: #f0f8ff;
  border: 1px solid #e3f2fd;
  border-radius: 6px;
  padding: 0.75rem;
  margin: 0.5rem 0;
}

.ticket-detail {
  margin: 0.25rem 0;
  font-size: 0.85rem;
  color: #555;
}

.ticket-detail strong {
  color: #1976D2;
  font-weight: 600;
}

.ticket-description {
  color: #555;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 0.75rem;
}

.ticket-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: #888;
}

.ticket-actions {
  display: flex;
  gap: 0.75rem;
}

.edit-button,
.delete-button,
.download-button {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.edit-button {
  background: #ff9800;
  color: white;
}

.edit-button:hover {
  background: #f57c00;
}

.delete-button {
  background: #f44336;
  color: white;
}

.delete-button:hover {
  background: #d32f2f;
}

.download-button {
  background: #4caf50;
  color: white;
}

.download-button:hover {
  background: #388e3c;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.page-button {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  background: white;
  color: #333;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-button:hover:not(:disabled) {
  background: #f5f5f5;
  border-color: #2196F3;
}

.page-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
}

.page-number {
  padding: 0.5rem 0.75rem;
  border: 1px solid #ddd;
  background: white;
  color: #333;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 40px;
}

.page-number:hover {
  background: #f5f5f5;
  border-color: #2196F3;
}

.page-number.active {
  background: #2196F3;
  color: white;
  border-color: #2196F3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ticket-card {
    padding: 1rem;
  }

  .ticket-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .ticket-meta {
    flex-direction: column;
    gap: 0.25rem;
  }

  .ticket-actions {
    flex-direction: column;
  }

  .pagination {
    flex-wrap: wrap;
  }

  .page-numbers {
    order: -1;
    width: 100%;
    justify-content: center;
    margin-bottom: 0.5rem;
  }
}
