import React, { useState, useEffect, useContext } from 'react'
import { useNavigate } from 'react-router-dom'
import './TruckerMaterialTicketing.css'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { isWithinGeofence } from '../utils/geofencing'
import { PurchaseOrderContext } from '../contexts/PurchaseOrderContext'
import { MapContainer, Circle, Popup, TileLayer, Marker } from 'react-leaflet'

// We'll use this directly where needed instead of as a separate function

// Fix for Leaflet marker icon issue
const defaultIcon = L.icon({
  iconUrl: '/marker-icon.png',
  shadowUrl: '/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
})

L.Marker.prototype.options.icon = defaultIcon

interface PurchaseOrder {
  _id: string
  jobDetails: string
  status: string
  clientId?: string | {
    _id?: string
    name: string
    email: string
  }
  haulerId?: {
    name: string
    email: string
  }
  haulerRates?: Array<{
    materialType: string
    rate: number
    unit: string
  }>
  geofence?: {
    type: 'Polygon' | 'Circle'
    coordinates?: number[][]
    center?: [number, number]
    radius?: number
  }
}

interface Location {
  lat: number
  lng: number
}

const TruckerMaterialTicketing: React.FC = () => {
  const navigate = useNavigate()
  const context = useContext(PurchaseOrderContext)
  
  if (!context) {
    throw new Error('TruckerMaterialTicketing must be used within a PurchaseOrderProvider')
  }
  
  const { purchaseOrders, loading: poLoading, updatePurchaseOrderStatus } = context
  
  // State variables
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null)
  const [locationError, setLocationError] = useState<string | null>(null)
  const [isWithinJobSite, setIsWithinJobSite] = useState<boolean>(false)
  const [selectedPO, setSelectedPO] = useState<PurchaseOrder | null>(null)
  const [acceptedPOs, setAcceptedPOs] = useState<string[]>([])
  const [completedPOs, setCompletedPOs] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<'available' | 'accepted' | 'completed'>('available')
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [devMode, setDevMode] = useState<boolean>(() => {
    // Check if dev mode was previously enabled
    const savedDevMode = localStorage.getItem('truckerDevMode')
    return savedDevMode === 'true'
  })
  
  // Get trucker info from localStorage
  const getTruckerInfo = () => {
    const truckerInfo = localStorage.getItem('truckerInfo')
    return truckerInfo ? JSON.parse(truckerInfo) : null
  }
  
  // Get accepted and completed POs from localStorage
  useEffect(() => {
    const savedAcceptedPOs = localStorage.getItem('acceptedPOs')
    if (savedAcceptedPOs) {
      setAcceptedPOs(JSON.parse(savedAcceptedPOs))
    }
    
    const savedCompletedPOs = localStorage.getItem('completedPOs')
    if (savedCompletedPOs) {
      setCompletedPOs(JSON.parse(savedCompletedPOs))
    }
    
    // Check if trucker is logged in
    const truckerInfo = getTruckerInfo()
    if (!truckerInfo) {
      navigate('/trucker-login')
    }
  }, [navigate])
  
  // Get current location and update it periodically
  useEffect(() => {
    const watchId = navigator.geolocation.watchPosition(
      (position) => {
        const { latitude, longitude } = position.coords
        setCurrentLocation({ lat: latitude, lng: longitude })
        setLocationError(null)
      },
      (error) => {
        setLocationError(`Error getting location: ${error.message}`)
      },
      { enableHighAccuracy: true }
    )
    
    return () => {
      navigator.geolocation.clearWatch(watchId)
    }
  }, [])
  
  // Check if trucker is within job site geofence
  useEffect(() => {
    // If dev mode is enabled, always consider within job site
    if (devMode) {
      setIsWithinJobSite(true)
      return
    }
    
    if (currentLocation && selectedPO?.geofence) {
      const geofence = selectedPO.geofence
      
      if (geofence.type === 'Circle' && geofence.center && geofence.radius) {
        const isWithin = isWithinGeofence(
          currentLocation,
          {
            type: 'Circle',
            center: geofence.center,
            radius: geofence.radius
          }
        )
        setIsWithinJobSite(isWithin)
      }
    } else {
      setIsWithinJobSite(false)
    }
  }, [currentLocation, selectedPO])
  
  // Filter purchase orders based on view mode
  const filteredPurchaseOrders = () => {
    if (!purchaseOrders) return []
    
    console.log('Available POs:', purchaseOrders.length)
    console.log('PO statuses:', purchaseOrders.map(po => po.status))
    
    switch(viewMode) {
      case 'accepted':
        // Show POs that have been accepted by this trucker (local state)
        return purchaseOrders.filter((po: any) => acceptedPOs.includes(po._id) || po.status === 'accepted')
      case 'completed':
        return purchaseOrders.filter((po: any) => completedPOs.includes(po._id) || po.status === 'completed')
      case 'available':
      default:
        return purchaseOrders.filter((po: any) => 
          !acceptedPOs.includes(po._id) && 
          !completedPOs.includes(po._id) &&
          ['open', 'available', 'pending'].includes(po.status) &&
          po.status !== 'accepted' &&
          po.status !== 'completed'
        )
    }
  }
  
  // Handle PO selection
  const handleSelectPO = (po: PurchaseOrder) => {
    setSelectedPO(po)
  }
  
  // Handle accepting a purchase order - now implemented inline in the button
  /*const handleAcceptPO = async () => {
    if (!selectedPO) {
      setError('No purchase order selected')
      return
    }
    
    // Check if within job site (isWithinJobSite is already true if devMode is enabled)
    if (!isWithinJobSite) {
      setError('You must be within the job site to accept this purchase order')
      return
    }
    
    setLoading(true)
    setError(null)
    
    // Get trucker info
    const truckerInfo = getTruckerInfo()
    if (!truckerInfo) {
      setError('Trucker information not found. Please log in again.')
      setLoading(false)
      return
    }
    
    try {
      console.log('Accepting purchase order:', selectedPO._id)
      
      // For development purposes, simulate a successful API call
      // In a production environment, this would be a real API call
      // axios.post(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/purchase-orders/${selectedPO._id}/accept`, {
      //   truckerId: truckerInfo._id,
      //   truckerName: truckerInfo.name
      // })
      
      // Simulate API response
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Update local state - only allow one accepted PO at a time
      // This ensures the trucker can only work with one PO until completion
      const updatedAcceptedPOs = [selectedPO._id]
      setAcceptedPOs(updatedAcceptedPOs)
      
      // Save to localStorage
      localStorage.setItem('acceptedPOs', JSON.stringify(updatedAcceptedPOs))
      
      // Show success message and switch to accepted view
      setSuccess(`Successfully accepted purchase order ${selectedPO._id}`)
      setShowAcceptedOnly(true)
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null)
      }, 3000)
    } catch (err) {
      console.error('Error accepting purchase order:', err)
      setError('Failed to accept purchase order. Please try again.')
    } finally {
      setLoading(false)
    }
  }
  
  // Handle completing a purchase order delivery - now implemented inline in the button
  /*const handleCompletePO = async (poId: string) => {
    // Check if within job site (isWithinJobSite is already true if devMode is enabled)
    if (!isWithinJobSite) {
      setError('You must be within the job site to complete this delivery')
      return
    }
    
    try {
      setLoading(true)
      setError(null)
      
      // Get trucker info from localStorage
      const truckerInfo = getTruckerInfo()
      if (!truckerInfo) {
        throw new Error('Not authenticated as trucker')
      }
      
      console.log('Completing purchase order:', poId)
      
      // For development purposes, simulate a successful API call
      // In a production environment, this would be a real API call
      // Create completion ticket data
      // const completionData = {
      //   poId,
      //   driverName: truckerInfo.name || 'Unknown Driver',
      //   location: currentLocation,
      //   completionTime: new Date().toISOString()
      // }
      // 
      // const response = await axios.post(
      //   `${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/tickets/complete-delivery`,
      //   completionData,
      //   {
      //     headers: { Authorization: `Bearer ${truckerInfo.token}` }
      //   }
      // )
      
      // Simulate API response
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Update local state - remove from accepted POs
      const updatedAcceptedPOs = acceptedPOs.filter(id => id !== poId)
      setAcceptedPOs(updatedAcceptedPOs)
      
      // Save to localStorage
      localStorage.setItem('acceptedPOs', JSON.stringify(updatedAcceptedPOs))
      
      // Show success message
      setSuccess(`Delivery completed successfully. Ticket created.`)
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null)
      }, 3000)
      
      // If no more accepted POs, switch back to available view
      if (updatedAcceptedPOs.length === 0) {
        setShowAcceptedOnly(false)
      }
    } catch (err) {
      console.error('Error completing purchase order:', err)
      setError(`Error completing delivery: ${err instanceof Error ? err.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }*/
  
  // Toggle between available, accepted, and completed POs
  const togglePOView = (mode: 'available' | 'accepted' | 'completed') => {
    setViewMode(mode)
  }
  
  // Toggle developer mode
  const toggleDevMode = () => {
    const newDevMode = !devMode
    setDevMode(newDevMode)
    localStorage.setItem('truckerDevMode', String(newDevMode))
    
    // If enabling dev mode, update isWithinJobSite immediately
    if (newDevMode) {
      setIsWithinJobSite(true)
    } else if (currentLocation && selectedPO?.geofence) {
      // If disabling dev mode, recheck actual geofence status
      const geofence = selectedPO.geofence
      if (geofence.type === 'Circle' && geofence.center && geofence.radius) {
        const isWithin = isWithinGeofence(
          currentLocation,
          {
            type: 'Circle',
            center: geofence.center,
            radius: geofence.radius
          }
        )
        setIsWithinJobSite(isWithin)
      }
    } else {
      setIsWithinJobSite(false)
    }
  }
  
  // Logout function
  const handleLogout = () => {
    localStorage.removeItem('truckerInfo')
    navigate('/trucker-login')
  }
  
  return (
    <div className="trucker-material-ticketing-container">
      {/* Header */}
      <div className="page-header">
        <div className="page-title">
          <h1>Trucker Material Ticketing</h1>
          <p>Accept and complete material deliveries</p>
        </div>
        
        <button className="logout-button" onClick={handleLogout}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path fillRule="evenodd" d="M10 12.5a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v2a.5.5 0 0 0 1 0v-2A1.5 1.5 0 0 0 9.5 2h-8A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-2a.5.5 0 0 0-1 0v2z"/>
            <path fillRule="evenodd" d="M15.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 0 0-.708.708L14.293 7.5H5.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"/>
          </svg>
          <span>Logout</span>
        </button>
      </div>
      
      {/* Toggle buttons */}
      <div className="toggle-container">
        <button 
          className={`toggle-btn ${viewMode === 'available' ? 'active' : ''}`}
          onClick={() => togglePOView('available')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style={{marginRight: '8px'}}>
            <path d="M0 2a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2zm15 0a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V2z"/>
          </svg>
          Available POs
        </button>
        <button 
          className={`toggle-btn ${viewMode === 'accepted' ? 'active' : ''}`}
          onClick={() => togglePOView('accepted')}
          disabled={acceptedPOs.length === 0}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style={{marginRight: '8px'}}>
            <path d="M14 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"/>
            <path d="M10.97 4.97a.75.75 0 0 1 1.071 1.05l-3.992 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.235.235 0 0 1 .02-.022z"/>
          </svg>
          Accepted POs
        </button>
        <button 
          className={`toggle-btn ${viewMode === 'completed' ? 'active' : ''}`}
          onClick={() => togglePOView('completed')}
          disabled={completedPOs.length === 0}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style={{marginRight: '8px'}}>
            <path d="M14 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"/>
            <path d="M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z"/>
          </svg>
          Completed POs
        </button>
        
        {/* Developer Mode Toggle */}
        <div className="dev-mode-toggle">
          <label className="dev-mode-label">
            <input 
              type="checkbox" 
              checked={devMode} 
              onChange={toggleDevMode} 
            />
            <span>Developer Mode {devMode ? '(Bypass Geofence Active)' : ''}</span>
          </label>
        </div>
      </div>
      
      {/* Geofence Status Indicator */}
      <div className={`geofence-status ${isWithinJobSite ? 'within' : 'outside'} ${devMode ? 'dev-mode' : ''}`}>
        <div className="status-icon">
          {isWithinJobSite ? (
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
              <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
              <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
              <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
              <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>
            </svg>
          )}
        </div>
        <div className="status-text">
          <strong>Geofence Status:</strong> {isWithinJobSite ? 'Within Job Site' : 'Outside Job Site'}
          {devMode && <span className="dev-mode-indicator"> (Developer Mode Active)</span>}
        </div>
      </div>
      
      {/* Error and success messages */}
      {error && (
        <div className="error-message">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
            <path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"/>
          </svg>
          {error}
        </div>
      )}
      {success && (
        <div className="success-message">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
            <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
          </svg>
          {success}
        </div>
      )}
      {locationError && <div className="error-message">{locationError}</div>}
      
      {/* Loading indicator */}
      {(loading || poLoading) && <div className="loading">Loading...</div>}
      
      {/* Map display */}
      <div className="map-container">
        {currentLocation && (
          <MapContainer
            center={[currentLocation.lat, currentLocation.lng]}
            zoom={13}
            style={{ height: '300px', width: '100%' }}
          >
            <TileLayer
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            />
            
            {/* Driver location marker */}
            <Marker position={[currentLocation.lat, currentLocation.lng]}>
              <Popup>Your current location</Popup>
            </Marker>
            
            {/* Job site geofence circle */}
            {selectedPO?.geofence?.type === 'Circle' && 
             selectedPO?.geofence?.center && 
             selectedPO?.geofence?.radius && (
              <Circle
                center={[selectedPO.geofence.center[0], selectedPO.geofence.center[1]]}
                radius={selectedPO.geofence.radius}
                pathOptions={{
                  color: isWithinJobSite ? 'green' : 'red',
                  fillColor: isWithinJobSite ? 'green' : 'red',
                  fillOpacity: 0.2
                }}
              >
                <Popup>
                  <div className="map-popup">
                    <h3>Job Site Geofence</h3>
                    <div className={`popup-status ${isWithinJobSite ? 'within' : 'outside'}`}>
                      <div className="status-icon">
                        {isWithinJobSite ? (
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                            <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                          </svg>
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                            <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>
                          </svg>
                        )}
                      </div>
                      <div className="status-text">
                        {isWithinJobSite ? 'You are within the job site' : 'You are outside the job site'}
                      </div>
                    </div>
                    {devMode && (
                      <div className="dev-mode-popup-note">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                          <path d="M5.255 5.786a.237.237 0 0 0 .241.247h.825c.138 0 .248-.113.266-.25.09-.656.54-1.134 1.342-1.134.686 0 1.314.343 1.314 1.168 0 .635-.374.927-.965 1.371-.673.489-1.206 1.06-1.168 1.987l.003.217a.25.25 0 0 0 .25.246h.811a.25.25 0 0 0 .25-.25v-.105c0-.718.273-.927 1.01-1.486.609-.463 1.244-.977 1.244-2.056 0-1.511-1.276-2.241-2.673-2.241-1.267 0-2.655.59-2.75 2.286zm1.557 5.763c0 .533.425.927 1.01.927.609 0 1.028-.394 1.028-.927 0-.552-.42-.94-1.029-.94-.584 0-1.009.388-1.009.94z"/>
                        </svg>
                        <span>
                          <strong>Developer Mode Active</strong><br />
                          Geofence restrictions are bypassed
                        </span>
                      </div>
                    )}
                  </div>
                </Popup>
              </Circle>
            )}
          </MapContainer>
        )}
        
        <div className="location-status">
          <p>
            Location Status: 
            {isWithinJobSite 
              ? <span className="status-ok">Within Job Site</span> 
              : <span className="status-error">Outside Job Site</span>
            }
          </p>
        </div>
      </div>
      
      {/* Purchase Orders List */}
      <div className="po-list-container">
        <h2>
          {viewMode === 'accepted' ? 'Your Accepted Purchase Orders' : 
           viewMode === 'completed' ? 'Your Completed Purchase Orders' : 
           'Available Purchase Orders'}
        </h2>
        
        {filteredPurchaseOrders().length === 0 ? (
          <p className="no-po-message">
            {viewMode === 'accepted' ? 'You have no accepted purchase orders.' : 
             viewMode === 'completed' ? 'You have no completed purchase orders.' : 
             'No available purchase orders found.'}
          </p>
        ) : (
          <div className="po-list">
            {filteredPurchaseOrders().map(po => (
              <div 
                key={po._id} 
                className={`po-item ${selectedPO?._id === po._id ? 'selected' : ''} ${isWithinJobSite && selectedPO?._id === po._id ? 'within-geofence' : ''}`}
                onClick={() => handleSelectPO(po)}
              >
                <div className="po-details">
                  <h3>PO ID: {po._id}</h3>
                  <p>Job Details: {po.jobDetails}</p>
                  <p>Client: {
                    typeof po.clientId === 'object' && po.clientId 
                      ? po.clientId.name 
                      : 'Unknown Client'
                  }</p>
                  <p>Status: {po.status}</p>
                  
                  {/* Material Rates */}
                  {po.haulerRates && po.haulerRates.length > 0 && (
                    <div className="rates">
                      <h4>Material Rates:</h4>
                      <ul>
                        {po.haulerRates.map((rate: any, index: number) => (
                          <li key={index}>
                            {rate.materialType}: ${rate.rate} per {rate.unit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {/* Action buttons */}
                  <div className="po-actions">
                    {viewMode === 'accepted' ? (
                      <button 
                        onClick={() => {
                          // Check if within job site (isWithinJobSite is already true if devMode is enabled)
                          if (!isWithinJobSite) {
                            setError('You must be within the job site to complete this delivery');
                            return;
                          }
                          
                          // Set loading state immediately for better UX
                          setLoading(true);
                          setError(null);
                          
                          // Simulate API call with a timeout
                          // First, update the purchase order status in the backend
                          const handlePurchaseOrderCompletion = async () => {
                            try {
                              // Update the purchase order status in the backend using the context method
                              try {
                                // This will update the status in the backend and refresh the purchase orders
                                await updatePurchaseOrderStatus(po._id, 'completed');
                                console.log(`Successfully updated PO ${po._id} status to completed in backend`);
                              } catch (apiError) {
                                console.error('API error updating PO status:', apiError);
                                // Continue with local updates even if API fails
                              }
                              
                              // Update local state - move from accepted POs to completed POs
                              const updatedAcceptedPOs = acceptedPOs.filter(id => id !== po._id);
                              setAcceptedPOs(updatedAcceptedPOs);
                              
                              // Add to completed POs
                              const updatedCompletedPOs = [...completedPOs, po._id];
                              setCompletedPOs(updatedCompletedPOs);
                              
                              // Save to localStorage
                              localStorage.setItem('acceptedPOs', JSON.stringify(updatedAcceptedPOs));
                              localStorage.setItem('completedPOs', JSON.stringify(updatedCompletedPOs));
                              
                              // Show success message
                              setSuccess(`Delivery completed successfully. Ticket created.`);
                              setLoading(false);
                              
                              // If no more accepted POs, switch back to available view
                              if (updatedAcceptedPOs.length === 0) {
                                togglePOView('available');
                              }
                              
                              // Clear success message after 3 seconds
                              setTimeout(() => {
                                setSuccess(null);
                              }, 3000);
                            } catch (error) {
                              console.error('Error completing purchase order:', error);
                              setError('Failed to complete delivery. Please try again.');
                              setLoading(false);
                            }
                          };
                          
                          // Execute the update function
                          handlePurchaseOrderCompletion();
                        }}
                        disabled={!isWithinJobSite || loading}
                        className="complete-btn"
                      >
                        {loading ? (
                          <>
                            <div className="spinner"></div>
                            Processing...
                          </>
                        ) : (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                              <path d="M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z"/>
                            </svg>
                            Complete Delivery
                          </>
                        )}
                      </button>
                    ) : viewMode === 'completed' ? (
                      <div className="completed-status">
                        <span className="status-badge completed">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z"/>
                          </svg>
                          Completed
                        </span>
                      </div>
                    ) : (
                      <button 
                        onClick={() => {
                          if (selectedPO && selectedPO._id === po._id) {
                            // Set loading state immediately for better UX
                            setLoading(true);
                            // Clear any previous errors
                            setError(null);
                            
                            // Update purchase order status in the backend
                          const handlePurchaseOrderAcceptance = async () => {
                            try {
                              // Update the purchase order status in the backend using the context method
                              try {
                                // This will update the status in the backend and refresh the purchase orders
                                await updatePurchaseOrderStatus(po._id, 'accepted');
                                console.log(`Successfully updated PO ${po._id} status to accepted in backend`);
                              } catch (apiError) {
                                console.error('API error updating PO status:', apiError);
                                // Continue with local updates even if API fails
                              }
                              
                              // Update local state
                              setAcceptedPOs([...acceptedPOs, po._id]);
                              
                              // Save to localStorage
                              localStorage.setItem('acceptedPOs', JSON.stringify([...acceptedPOs, po._id]));
                              
                              // Show success message
                              setSuccess(`Purchase Order accepted successfully.`);
                              setLoading(false);
                              
                              // Switch to accepted view
                              togglePOView('accepted');
                              
                              // Clear success message after 3 seconds
                              setTimeout(() => {
                                setSuccess(null);
                              }, 3000);
                            } catch (error) {
                              console.error('Error accepting purchase order:', error);
                              setError('Failed to accept purchase order. Please try again.');
                              setLoading(false);
                            }
                          };
                          
                          // Execute the update function
                          handlePurchaseOrderAcceptance();
                          }
                        }}
                        disabled={selectedPO?._id !== po._id || !isWithinJobSite || loading}
                        className="accept-btn"
                      >
                        {loading ? (
                          <>
                            <div className="spinner"></div>
                            Processing...
                          </>
                        ) : (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                              <path d="M10.97 4.97a.75.75 0 0 1 1.071 1.05l-3.992 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.235.235 0 0 1 .02-.022z"/>
                            </svg>
                            Accept Purchase Order
                          </>
                        )}
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default TruckerMaterialTicketing
