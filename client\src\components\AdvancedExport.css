/* Advanced Export Styles */
.advanced-export {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e0e0e0;
}

.advanced-export h3 {
  color: #2c3e50;
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.advanced-export h4 {
  color: #34495e;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Format Selection */
.format-selection {
  margin-bottom: 2rem;
}

.format-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.format-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.format-card:hover {
  border-color: #2196F3;
  background: #f0f8ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
}

.format-card.selected {
  border-color: #2196F3;
  background: #e3f2fd;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.format-card.recommended::after {
  content: '⭐ Recommended';
  position: absolute;
  top: -8px;
  right: 8px;
  background: #4caf50;
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-weight: 600;
}

.format-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.format-info {
  flex: 1;
}

.format-info h5 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
}

.format-info p {
  margin: 0;
  color: #666;
  font-size: 0.85rem;
  line-height: 1.3;
}

.recommended-badge {
  display: inline-block;
  background: #4caf50;
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-weight: 600;
  margin-top: 0.25rem;
}

/* Column Selection */
.column-selection {
  margin-bottom: 2rem;
}

.column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.column-actions {
  display: flex;
  gap: 0.5rem;
}

.column-actions button {
  padding: 0.4rem 0.8rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  color: #666;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.column-actions button:hover {
  background: #f5f5f5;
  border-color: #bbb;
}

.column-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
  padding: 1rem;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.column-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  font-size: 0.9rem;
}

.column-checkbox:hover {
  background: #f5f5f5;
}

.column-checkbox input[type="checkbox"] {
  margin: 0;
}

.selected-columns-summary {
  color: #666;
  font-size: 0.9rem;
  font-style: italic;
}

/* Export Options */
.export-options {
  margin-bottom: 2rem;
}

.options-grid {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.option-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.9rem;
}

.option-checkbox:hover {
  background: #f5f5f5;
}

.option-checkbox input[type="checkbox"] {
  margin: 0;
}

/* Export Actions */
.export-actions {
  text-align: center;
}

.export-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
  justify-content: center;
}

.export-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.export-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.export-icon {
  font-size: 1.1rem;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .format-grid {
    grid-template-columns: 1fr;
  }

  .column-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .column-actions {
    width: 100%;
    justify-content: space-between;
  }

  .column-grid {
    grid-template-columns: 1fr;
  }

  .export-button {
    width: 100%;
    padding: 1.2rem;
  }
}

@media (max-width: 480px) {
  .advanced-export {
    padding: 1rem;
  }

  .format-card {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .format-icon {
    font-size: 1.5rem;
  }

  .column-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .column-actions button {
    width: 100%;
    text-align: center;
  }
}
