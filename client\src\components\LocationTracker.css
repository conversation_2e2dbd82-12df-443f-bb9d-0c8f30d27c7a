.location-tracker {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tracker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tracker-header h2 {
  margin: 0;
  color: #333;
}

.tracking-button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  background: #4CAF50;
  color: white;
}

.tracking-button:hover {
  background: #45a049;
}

.tracking-button.active {
  background: #f44336;
}

.tracking-button.active:hover {
  background: #d32f2f;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  color: #c62828;
  margin-bottom: 20px;
}

.location-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-group label {
  font-weight: 500;
  color: #333;
}

.info-group p {
  margin: 0;
  color: #555;
}

.status {
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

.status.within-range {
  background: #e8f5e9;
  color: #2e7d32;
}

.status.out-of-range {
  background: #ffebee;
  color: #c62828;
}

.geofence-list {
  margin-top: 30px;
}

.geofence-list h3 {
  margin-bottom: 15px;
  color: #333;
}

.geofence-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 10px;
}

.geofence-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
}

.geofence-info p {
  margin: 0;
  color: #555;
}

.distance-info {
  text-align: right;
}

.distance-info p {
  margin: 0;
  color: #666;
  font-weight: 500;
}

@media (max-width: 768px) {
  .tracker-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .location-info {
    grid-template-columns: 1fr;
  }

  .geofence-item {
    flex-direction: column;
    gap: 15px;
  }

  .distance-info {
    text-align: left;
  }
} 