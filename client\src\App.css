.app {
  min-height: 100vh;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #ffffff;
  background-color: #1e1e1e;
}

/* Base text color for all content */
.app * {
  color: inherit;
}

/* Specific overrides for components that need dark text for contrast */
.form-group input, 
.form-group select, 
.form-group textarea,
.table td,
.card-content,
.dropdown-content,
.modal-content {
  color: #111827;
}

/* Exception for headers that should remain white */
.page-header,
.admin-header,
.section-header h2,
.section-header-with-button h2 {
  color: white;
}

.page-header *,
.admin-header * {
  color: inherit;
}

/* Global button styles */
button {
  font-family: inherit;
}

/* Global form styles */
input, select, textarea {
  font-family: inherit;
}

/* Utility classes */
.text-center {
  text-align: center;
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }