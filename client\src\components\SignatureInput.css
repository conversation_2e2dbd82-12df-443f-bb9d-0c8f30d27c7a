/* Mobile-first design for SignatureInput */
.signature-input {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.signature-label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

/* Add Signature Button */
.add-signature-btn {
  padding: 1rem;
  border: 2px dashed #2196F3;
  border-radius: 8px;
  background: #f8f9ff;
  color: #2196F3;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.add-signature-btn:hover {
  background: #e3f2fd;
  border-color: #1976d2;
  transform: translateY(-1px);
}

.add-signature-btn:active {
  transform: translateY(0);
}

/* Signature Preview */
.signature-preview {
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  background: white;
}

.signature-image {
  width: 100%;
  max-height: 120px;
  object-fit: contain;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background: white;
}

.signature-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.edit-signature-btn,
.remove-signature-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  color: #666;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-signature-btn:hover {
  background: #f5f5f5;
  border-color: #2196F3;
  color: #2196F3;
}

.remove-signature-btn:hover {
  background: #ffebee;
  border-color: #f44336;
  color: #f44336;
}

/* Modal Overlay */
.signature-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

/* Modal Container */
.signature-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 100vw;
  max-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Modal Header */
.signature-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.signature-modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.close-modal-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-modal-btn:hover {
  background: #e0e0e0;
  color: #333;
}

/* Canvas Container */
.signature-canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  position: relative;
}

.signature-canvas {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 300px;
  cursor: crosshair;
  touch-action: none;
  background: white;
}

.signature-instructions {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 1.1rem;
  pointer-events: none;
  text-align: center;
  z-index: 1;
}

/* Modal Actions */
.signature-modal-actions {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.retry-btn,
.submit-signature-btn {
  flex: 1;
  padding: 1rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 50px;
}

.retry-btn {
  background: #f5f5f5;
  color: #666;
  border: 2px solid #ddd;
}

.retry-btn:hover {
  background: #e0e0e0;
  border-color: #bbb;
}

.submit-signature-btn {
  background: #2196F3;
  color: white;
}

.submit-signature-btn:hover:not(:disabled) {
  background: #1976d2;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.submit-signature-btn:disabled {
  background: #ccc;
  color: #999;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Mobile Optimizations */
@media (max-width: 600px) {
  .signature-modal-overlay {
    padding: 0;
  }
  
  .signature-modal {
    border-radius: 0;
    height: 100vh;
    max-height: 100vh;
  }
  
  .signature-canvas {
    min-height: 400px;
  }
  
  .signature-modal-actions {
    padding: 1rem;
  }
  
  .retry-btn,
  .submit-signature-btn {
    min-height: 60px;
    font-size: 1.1rem;
  }
  
  .add-signature-btn {
    min-height: 70px;
    font-size: 1.1rem;
  }
}

/* Landscape mobile */
@media (max-width: 900px) and (orientation: landscape) {
  .signature-modal-header {
    padding: 0.75rem 1rem;
  }
  
  .signature-modal-header h3 {
    font-size: 1rem;
  }
  
  .signature-modal-actions {
    padding: 1rem;
  }
  
  .signature-canvas {
    min-height: 250px;
  }
}

/* Prevent text selection during drawing */
.signature-canvas {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .add-signature-btn,
  .retry-btn,
  .submit-signature-btn,
  .edit-signature-btn,
  .remove-signature-btn,
  .close-modal-btn {
    transition: none;
  }
  
  .add-signature-btn:hover,
  .submit-signature-btn:hover:not(:disabled) {
    transform: none;
  }
}
