import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';

interface Rate {
  materialType: string;
  rate: number;
  unit: string;
}

interface Location {
  name: string;
  address: string;
  coordinates: {
    lat: number;
    lng: number;
  };
}

interface Load {
  materialType: string;
  quantity: number;
  unit: string;
}

interface PurchaseOrder {
  _id: string;
  haulerId: {
    name: string;
    email: string;
  };
  clientId: {
    name: string;
    email: string;
  };
  status: 'pending' | 'approved' | 'rejected' | 'open' | 'in_progress' | 'completed' | 'available' | 'accepted';
  haulerRates: Rate[];
  resaleRates: Rate[];
  jobDetails: string;
  startingLocation?: Location;
  deliveryLocations?: Location[];
  loads?: Load[];
  assignedTrucker?: {
    _id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
  approvedAt?: string;
  geofence?: {
    type: 'Polygon' | 'Circle';
    coordinates?: number[][];
    center?: [number, number];
    radius?: number;
  };
}

// Empty array for purchase orders - no mock data
const mockPurchaseOrders: PurchaseOrder[] = [];

/**
 * Defines the context type for managing purchase orders in the application.
 * Provides methods and state for creating, updating, and tracking purchase orders.
 * 
 * @interface PurchaseOrderContextType
 * @property {PurchaseOrder[]} purchaseOrders - List of current purchase orders
 * @property {boolean} loading - Indicates if purchase orders are currently being loaded
 * @property {string | null} error - Any error that occurred during purchase order operations
 * @property {Function} createPurchaseOrder - Method to create a new purchase order
 * @property {Function} updatePurchaseOrder - Method to update an existing purchase order
 * @property {Function} updatePurchaseOrderStatus - Method to update the status of a purchase order
 * @property {'hauler' | 'client'} userRole - Current user's role in the system
 * @property {boolean} useMockData - Flag to indicate if mock data is being used
 * @property {Function} setUseMockData - Method to toggle mock data usage
 * @property {string[]} availableStartingLocations - List of available starting locations
 * @property {string[]} availableDeliveryLocations - List of available delivery locations
 */
export interface PurchaseOrderContextType {  purchaseOrders: PurchaseOrder[];
  loading: boolean;
  error: string | null;
  createPurchaseOrder: (data: {
    startingLocation: string;
    deliveryLocations: string[];
    materialType: string;
    quantity: number;
    unit: string;
    jobDetails: string;
  }) => Promise<void>;
  updatePurchaseOrder: (id: string, data: any) => Promise<void>;
  updatePurchaseOrderStatus: (poId: string, status: 'approved' | 'rejected' | 'completed' | 'accepted') => Promise<void>;
  userRole: 'hauler' | 'client';
  useMockData: boolean;
  setUseMockData: (value: boolean) => void;
  availableStartingLocations: string[];
  availableDeliveryLocations: string[];
}

export const PurchaseOrderContext = createContext<PurchaseOrderContextType | undefined>(undefined);

export const PurchaseOrderProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userRole, setUserRole] = useState<'hauler' | 'client'>('hauler');
  const [useMockData, setUseMockData] = useState(false);
  const navigate = useNavigate();

  // Get user role from localStorage on mount
  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      const user = JSON.parse(storedUser);
      setUserRole(user.role);
    }

    // Check if mock data mode is enabled
    const mockDataEnabled = localStorage.getItem('useMockData');
    if (mockDataEnabled === 'true') {
      setUseMockData(true);
    }
  }, []);

  const fetchPurchaseOrders = async () => {
    try {
      setLoading(true);
      setError(null);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      if (useMockData) {
        // Use mock data
        console.log('Using mock purchase order data');
        setPurchaseOrders(mockPurchaseOrders);
        console.log('Mock purchase order statuses:', mockPurchaseOrders.map(po => ({ id: po._id, status: po.status })));
      } else {
        // Try to fetch from API
        try {
          console.log('Fetching purchase orders from API...');
          const response = await axios.get(`http://localhost:5000/api/purchase-orders/${userRole}`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          console.log('API returned purchase orders:', response.data.length);
          console.log('Purchase order statuses from API:', response.data.map((po: any) => ({ id: po._id, status: po.status })));
          setPurchaseOrders(response.data);
        } catch (apiError) {
          console.error('API error, falling back to mock data:', apiError);
          setUseMockData(true);
          setPurchaseOrders(mockPurchaseOrders);
          console.log('Fallback mock purchase order statuses:', mockPurchaseOrders.map(po => ({ id: po._id, status: po.status })));
        }
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to fetch purchase orders';
      setError(errorMessage);
      console.error('Error fetching purchase orders:', err);
      
      // Fall back to mock data if there's an authentication error
      if (err.message === 'No authentication token found' || err.response?.status === 401) {
        console.log('Authentication error, using mock data');
        setUseMockData(true);
        setPurchaseOrders(mockPurchaseOrders);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPurchaseOrders();
  }, [userRole, useMockData]);

  const createPurchaseOrder = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      
      if (useMockData) {
        // Create a real purchase order even in mock mode
        const newPO: PurchaseOrder = {
          _id: `po-${Date.now()}`,
          haulerId: {
            name: 'Current Hauler',
            email: '<EMAIL>'
          },
          clientId: {
            name: data.clientName || 'Client Company',
            email: data.clientEmail || '<EMAIL>'
          },
          status: 'pending',
          haulerRates: data.haulerRates || [],
          resaleRates: data.resaleRates || [],
          jobDetails: data.jobDetails || 'New Job',
          startingLocation: data.startingLocation,
          deliveryLocations: data.deliveryLocations,
          loads: data.loads,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        setPurchaseOrders([...purchaseOrders, newPO]);
        navigate('/purchase-orders');
        return;
      }
      
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      try {
        const response = await axios.post('http://localhost:5000/api/purchase-orders', data, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        setPurchaseOrders([...purchaseOrders, response.data]);
      } catch (apiError) {
        console.error('API error, using mock data:', apiError);
        setUseMockData(true);
        
        // Create a purchase order in mock mode
        const newPO: PurchaseOrder = {
          _id: `po-${Date.now()}`,
          haulerId: {
            name: 'Current Hauler',
            email: '<EMAIL>'
          },
          clientId: {
            name: data.clientName || 'Client Company',
            email: data.clientEmail || '<EMAIL>'
          },
          status: 'pending',
          haulerRates: data.haulerRates || [],
          resaleRates: data.resaleRates || [],
          jobDetails: data.jobDetails || 'New Job',
          startingLocation: data.startingLocation,
          deliveryLocations: data.deliveryLocations,
          loads: data.loads,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        setPurchaseOrders([...purchaseOrders, newPO]);
      }
      
      navigate('/purchase-orders');
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to create purchase order';
      setError(errorMessage);
      console.error('Error creating purchase order:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updatePurchaseOrder = async (id: string, data: any) => {
    try {
      setLoading(true);
      setError(null);
      
      if (useMockData) {
        // Update purchase order
        const updatedPOs = purchaseOrders.map(po => {
          if (po._id === id) {
            return {
              ...po,
              ...data,
              updatedAt: new Date().toISOString()
            };
          }
          return po;
        });
        
        setPurchaseOrders(updatedPOs);
        navigate('/purchase-orders');
        return;
      }
      
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      try {
        const response = await axios.patch(`http://localhost:5000/api/purchase-orders/${id}`, data, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        setPurchaseOrders(purchaseOrders.map(po => 
          po._id === id ? response.data : po
        ));
      } catch (apiError) {
        console.error('API error, using mock data:', apiError);
        setUseMockData(true);
        
        // Update purchase order in mock mode
        const updatedPOs = purchaseOrders.map(po => {
          if (po._id === id) {
            return {
              ...po,
              ...data,
              updatedAt: new Date().toISOString()
            };
          }
          return po;
        });
        
        setPurchaseOrders(updatedPOs);
      }
      
      navigate('/purchase-orders');
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to update purchase order';
      setError(errorMessage);
      console.error('Error updating purchase order:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updatePurchaseOrderStatus = async (poId: string, status: 'approved' | 'rejected' | 'completed' | 'accepted') => {
    console.log(`Updating purchase order ${poId} status to ${status}...`);
    try {
      setLoading(true);
      setError(null);
      
      if (useMockData) {
        // Update purchase order status
        const updatedPOs = purchaseOrders.map(po => {
          if (po._id === poId) {
            const updatedPO = {
              ...po,
              status,
              updatedAt: new Date().toISOString(),
              ...(status === 'approved' ? { approvedAt: new Date().toISOString() } : {})
            };
            console.log(`Mock data: Updated PO ${poId} status to ${status}`);
            return updatedPO;
          }
          return po;
        });
        
        setPurchaseOrders(updatedPOs);
        console.log('Updated purchase orders with mock data:', updatedPOs.map(po => ({ id: po._id, status: po.status })));
        return;
      }
      
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      try {
        const response = await axios.patch(`http://localhost:5000/api/purchase-orders/${poId}/status`, 
          { status },
          {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }
        );
        
        const updatedPO = response.data;
        console.log(`Backend update successful for PO ${poId}, new status: ${updatedPO.status}`);
        
        setPurchaseOrders(purchaseOrders.map(po => 
          po._id === poId ? updatedPO : po
        ));
        
        console.log('Updated purchase orders from API:', purchaseOrders.map(po => ({ id: po._id, status: po.status })));
      } catch (apiError) {
        console.error('API error, using mock data for status update:', apiError);
        setUseMockData(true);
        
        // Update purchase order status in mock mode
        const updatedPOs = purchaseOrders.map(po => {
          if (po._id === poId) {
            return {
              ...po,
              status,
              updatedAt: new Date().toISOString(),
              ...(status === 'approved' ? { approvedAt: new Date().toISOString() } : {})
            };
          }
          return po;
        });
        
        setPurchaseOrders(updatedPOs);
        console.log('Updated purchase orders with mock data (fallback):', updatedPOs.map(po => ({ id: po._id, status: po.status })));
      }
      
      await fetchPurchaseOrders();
      console.log('Refreshed purchase orders after status update');
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to update purchase order status';
      setError(errorMessage);
      console.error('Error updating purchase order status:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return (
    <PurchaseOrderContext.Provider
      value={{
        purchaseOrders,
        loading,
        error,
        createPurchaseOrder,
        updatePurchaseOrder,
        updatePurchaseOrderStatus,
        userRole,
        useMockData,
        setUseMockData
      }}
    >
      {children}
    </PurchaseOrderContext.Provider>
  );
};

export const usePurchaseOrders = () => {
  const context = useContext(PurchaseOrderContext);
  if (context === undefined) {
    throw new Error('usePurchaseOrders must be used within a PurchaseOrderProvider');
  }
  return context;
}; 