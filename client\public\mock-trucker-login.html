<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON>er <PERSON>gin</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f5f7fa;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
    }
    .login-card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 30px;
      width: 400px;
      max-width: 90%;
    }
    h1 {
      color: #2c3e50;
      margin-top: 0;
      margin-bottom: 24px;
      font-size: 24px;
    }
    p {
      color: #7f8c8d;
      margin-bottom: 24px;
      line-height: 1.5;
    }
    button {
      background-color: #3498db;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 12px 20px;
      font-size: 16px;
      cursor: pointer;
      width: 100%;
      transition: background-color 0.2s;
    }
    button:hover {
      background-color: #2980b9;
    }
    .loader {
      display: none;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      animation: spin 1s linear infinite;
      margin: 20px auto;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="login-card">
    <h1>Mock Trucker Login</h1>
    <p>Click the button below to log in as a test trucker (John Smith) with developer mode enabled. This will redirect you to the Trucker Material Ticketing page.</p>
    <button id="loginBtn">Log In as Test Trucker</button>
    <div id="loader" class="loader"></div>
  </div>

  <script>
    document.getElementById('loginBtn').addEventListener('click', function() {
      // Show loader
      this.textContent = 'Logging in...';
      document.getElementById('loader').style.display = 'block';
      this.disabled = true;
      
      // Create mock auth data
      const mockAuthData = {
        driverName: "John Smith",
        driverCode: "123",
        loginTime: new Date().toISOString(),
        token: "mock-token-" + Date.now()
      };
      
      // Set localStorage values
      localStorage.setItem('truckerAuth', JSON.stringify(mockAuthData));
      localStorage.setItem('truckerInfo', JSON.stringify(mockAuthData));
      localStorage.setItem('developerMode', JSON.stringify(true));
      
      console.log('Mock trucker login set up successfully');
      console.log('Auth data:', mockAuthData);
      
      // Redirect to trucker material ticketing page after a short delay
      setTimeout(function() {
        window.location.href = '/trucker-material-ticketing';
      }, 1000);
    });
  </script>
</body>
</html>
