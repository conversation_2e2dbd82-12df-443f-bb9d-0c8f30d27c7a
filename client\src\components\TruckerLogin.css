/* Mobile-first design */
.trucker-login-container {
  min-height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #2196F3 0%, #2196F3 30%, #1976D2 50%, #0D47A1 70%, #FF8F00 85%, #FFA000 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  padding: 1rem;
  position: relative;
}

.trucker-login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.15"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.15"/><circle cx="80" cy="30" r="0.5" fill="white" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
  animation: float 20s ease-in-out infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

/* Mobile-first: Compact login card */
.trucker-login-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  max-width: 100%;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
  animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile-first: Single column layout */
.login-layout {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 100%;
  position: relative;
  z-index: 1;
}

.login-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.back-button {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateX(-2px);
}

.login-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.truck-icon {
  color: #FFA000;
  filter: drop-shadow(0 0 8px rgba(255, 160, 0, 0.3));
}

.login-title h1 {
  color: white;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.login-description {
  margin-bottom: 2rem;
}

.login-description p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  line-height: 1.5;
  margin: 0;
  text-align: center;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  color: white;
  font-weight: 500;
  font-size: 0.95rem;
}

/* Mobile-first: Large touch targets */
.form-group input {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 1rem;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
  min-height: 48px;
  box-sizing: border-box;
}

.form-group input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.form-group input:focus {
  outline: none;
  border-color: #FFA000;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(255, 160, 0, 0.2);
}

.form-group input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 8px;
  padding: 0.75rem;
  font-size: 0.9rem;
}

/* Mobile-first: Large button */
.login-button {
  background: linear-gradient(135deg, #FFA000 0%, #FFB74D 100%);
  border: none;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  box-shadow: 0 4px 15px rgba(255, 160, 0, 0.3);
  min-height: 48px;
  width: 100%;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 160, 0, 0.4);
}

.login-button:active:not(:disabled) {
  transform: translateY(0);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.login-info {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.info-item svg {
  color: #FFA000;
  flex-shrink: 0;
}

.test-credentials-panel {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  max-width: 350px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.8s ease-out 0.3s both;
}

.test-credentials-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.test-credentials-header svg {
  color: #4CAF50;
  flex-shrink: 0;
}

.test-credentials-header h3 {
  color: white;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.dev-badge {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  color: white;
  font-size: 0.7rem;
  font-weight: 700;
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.test-credentials-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.test-credential-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.test-credential-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.credential-info {
  flex: 1;
  min-width: 0;
}

.credential-name {
  color: white;
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.credential-code {
  color: #FFA000;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.credential-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  line-height: 1.3;
}

.use-credential-btn {
  background: linear-gradient(135deg, #FFA000 0%, #FFB74D 100%);
  border: none;
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(255, 160, 0, 0.3);
}

.use-credential-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 160, 0, 0.4);
}

.use-credential-btn:active {
  transform: translateY(0);
}

.test-credentials-note {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.test-credentials-note svg {
  color: #2196F3;
  flex-shrink: 0;
}

/* Tablet and larger screens */
@media (min-width: 600px) {
  .trucker-login-container {
    padding: 2rem;
  }

  .trucker-login-card {
    padding: 2.5rem;
    max-width: 450px;
    border-radius: 20px;
  }

  .login-layout {
    flex-direction: row;
    gap: 2rem;
    align-items: flex-start;
    max-width: 1200px;
  }

  .test-credentials-panel {
    max-width: 350px;
    padding: 2rem;
    order: 0;
  }

  .login-title h1 {
    font-size: 1.8rem;
  }

  .login-button {
    font-size: 1rem;
    width: auto;
  }
}

/* Desktop screens */
@media (min-width: 1024px) {
  .login-layout {
    gap: 3rem;
  }

  .test-credentials-panel {
    max-width: 400px;
  }
}
