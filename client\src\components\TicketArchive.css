.ticket-archive {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  color: #333 !important;
}

/* Ensure all text in ticket archive is dark */
.ticket-archive * {
  color: inherit !important;
}

.ticket-archive h1,
.ticket-archive h2,
.ticket-archive h3,
.ticket-archive h4,
.ticket-archive h5,
.ticket-archive h6 {
  color: #333 !important;
}

.ticket-archive p,
.ticket-archive span,
.ticket-archive div,
.ticket-archive label,
.ticket-archive td,
.ticket-archive th {
  color: #333 !important;
}

.ticket-archive input,
.ticket-archive select,
.ticket-archive textarea {
  color: #333 !important;
}

.page-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.back-button {
  background: #2196F3;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1rem;
}

.back-button:hover {
  background: #1976D2;
  transform: translateY(-1px);
}

.page-header h1 {
  font-size: 2.5rem;
  color: #333;
  margin: 0 0 0.5rem 0;
  font-weight: 700;
}

.subtitle {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

.archive-main {
  max-width: 1200px;
  margin: 0 auto;
}

/* Filters Section */
.filters-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.filters-section h2 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.3rem;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.filter-group select,
.filter-group input {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: #2196F3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.clear-filters-btn {
  background: #ff9800;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  height: fit-content;
}

.clear-filters-btn:hover {
  background: #f57c00;
}

/* Results Summary */
.results-summary {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.summary-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.summary-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.reporting-button {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.reporting-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
}

.reporting-section {
  margin-top: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.results-summary p {
  margin: 0;
  color: #666;
  font-weight: 500;
}

@media (max-width: 768px) {
  .summary-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}

/* Tickets Table */
.tickets-table-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.table-container {
  overflow-x: auto;
}

.tickets-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.tickets-table th,
.tickets-table td {
  padding: 1rem 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.tickets-table th {
  background: #f5f5f5;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 10;
}

.tickets-table tr:hover {
  background: #f9f9f9;
}

.clickable-row {
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.clickable-row:hover {
  background-color: rgba(102, 126, 234, 0.08) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

.clickable-row::after {
  content: "👁️ Click for details";
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  white-space: nowrap;
}

.clickable-row:hover::after {
  opacity: 1;
}

.ticket-number {
  font-weight: 600;
  color: #1976D2;
}

.status-badge {
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.image-link {
  background: #4caf50;
  color: white;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image-link:hover {
  background: #388e3c;
  transform: translateY(-1px);
}

.no-image {
  color: #999;
  font-style: italic;
  font-size: 0.8rem;
}

.type-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.type-badge.manual {
  background: #9c27b0;
  color: white;
}

.type-badge.file {
  background: #607d8b;
  color: white;
}

/* Loading and Empty States */
.loading {
  text-align: center;
  padding: 3rem;
  color: #666;
  font-size: 1.1rem;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

/* Pagination */
.pagination-section {
  display: flex;
  justify-content: center;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.95);
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.page-button {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  background: white;
  color: #333;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-button:hover:not(:disabled) {
  background: #f5f5f5;
  border-color: #2196F3;
}

.page-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
}

.page-number {
  padding: 0.5rem 0.75rem;
  border: 1px solid #ddd;
  background: white;
  color: #333;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 40px;
}

.page-number:hover {
  background: #f5f5f5;
  border-color: #2196F3;
}

.page-number.active {
  background: #2196F3;
  color: white;
  border-color: #2196F3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ticket-archive {
    padding: 1rem;
  }

  .page-header {
    padding: 1.5rem;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .filters-grid {
    grid-template-columns: 1fr;
  }

  .tickets-table {
    font-size: 0.8rem;
  }

  .tickets-table th,
  .tickets-table td {
    padding: 0.75rem 0.5rem;
  }

  .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }
}
