const mongoose = require('mongoose');

const locationSchema = new mongoose.Schema({
  name: String,
  address: String,
  coordinates: {
    lat: Number,
    lng: Number
  }
}, { _id: false });

const loadSchema = new mongoose.Schema({
  materialType: String,
  quantity: Number,
  unit: String
}, { _id: false });

const purchaseOrderSchema = new mongoose.Schema({
  haulerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  clientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'open', 'in_progress', 'completed', 'available', 'accepted'],
    default: 'pending'
  },
  haulerRates: {
    type: [
      {
        materialType: String,
        rate: Number,
        unit: String
      }
    ],
    required: true
  },
  resaleRates: {
    type: [
      {
        materialType: String,
        rate: Number,
        unit: String
      }
    ],
    default: []
  },
  jobDetails: {
    type: String,
    required: true
  },
  startingLocation: {
    type: locationSchema,
    required: true
  },
  deliveryLocations: {
    type: [locationSchema],
    required: true,
    validate: [arr => arr.length > 0, 'At least one delivery location is required']
  },
  loads: {
    type: [loadSchema],
    default: []
  },
  assignedTrucker: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  approvedAt: Date,
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  // Add geofence for pickup/drop-off validation
  geofence: {
    type: {
      type: String,
      enum: ['Polygon', 'Circle'],
      default: 'Polygon'
    },
    coordinates: {
      type: Array, // For Polygon: [ [lng, lat], ... ]
      default: []
    },
    center: {
      type: [Number], // For Circle: [lng, lat]
      default: undefined
    },
    radius: Number // meters, for Circle
  },
  // Link tickets to this PO
  tickets: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Ticket' }]
});

// Update the updatedAt timestamp before saving
purchaseOrderSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('PurchaseOrder', purchaseOrderSchema); 