const express = require('express');
const router = express.Router();
const transactionController = require('../controllers/transactionController');
const auth = require('../middleware/auth');

// Get available purchase orders for a trucker based on geolocation
router.get('/available-pos', auth, transactionController.getAvailablePurchaseOrders);

// Transaction lifecycle endpoints
router.post('/pickup/start', auth, transactionController.startPickup);
router.put('/pickup/:transactionId/complete', auth, transactionController.completePickup);
router.put('/dump/:transactionId/start', auth, transactionController.startDump);
router.put('/dump/:transactionId/complete', auth, transactionController.completeDump);

// Get transactions by purchase order
router.get('/po/:purchaseOrderId', auth, transactionController.getTransactionsByPurchaseOrder);

// Get open transactions for a trucker
router.get('/trucker/open', auth, transactionController.getTruckerOpenTransactions);

// Admin endpoint to close a purchase order
router.put('/po/:purchaseOrderId/close', auth, transactionController.closePurchaseOrder);

module.exports = router;
