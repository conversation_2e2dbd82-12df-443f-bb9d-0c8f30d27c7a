.home-container {
  min-height: 100vh;
  width: 100vw;
  max-width: 100vw;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #2196F3 0%, #2196F3 30%, #1976D2 50%, #0D47A1 70%, #FF8F00 85%, #FFA000 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  color: white;
  box-sizing: border-box;
  /* Enable smooth scrolling on mobile */
  -webkit-overflow-scrolling: touch;
}

/* Desktop: Fixed positioning and no scroll */
@media (min-width: 769px) {
  .home-container {
    height: 100vh;
    max-height: 100vh;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
  }
}

/* Mobile: Allow scrolling */
@media (max-width: 768px) {
  .home-container {
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
  }
}

/* Home page specific styles */

.home-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.15"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.15"/><circle cx="80" cy="30" r="0.5" fill="white" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
  animation: float 20s ease-in-out infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

/* Mobile-first: Compact header */
.home-header {
  text-align: center;
  padding: 1rem 1rem 1rem;
  position: relative;
  z-index: 1;
  animation: slideInDown 1s ease-out;
}

.header-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 1rem;
  animation: fadeInRight 1s ease-out 0.5s both;
}

.developer-mode-toggle {
  display: flex;
  align-items: center;
}

.toggle-switch {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  user-select: none;
}

.toggle-switch input {
  display: none;
}

.toggle-slider {
  position: relative;
  width: 44px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 24px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 18px;
  height: 18px;
  background: white;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch input:checked + .toggle-slider {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  border-color: rgba(76, 175, 80, 0.5);
}

.toggle-switch input:checked + .toggle-slider::before {
  transform: translateX(20px);
}

.toggle-label {
  color: white;
  font-size: 0.85rem;
  font-weight: 500;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.trucker-login-button {
  margin-left: 10px;
}

.trucker-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #2c3e50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.trucker-btn:hover {
  background-color: #34495e;
}

/* Quick test login button */
.quick-test-login-button {
  margin-left: 10px;
}

.test-login-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.test-login-btn:hover {
  background-color: #c0392b;
}

/* Quick PO login button */
.quick-po-login-button {
  margin-left: 10px;
}

.po-login-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #27ae60;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.po-login-btn:hover {
  background-color: #2ecc71;
}

.trucker-btn:active {
  transform: translateY(0);
}

.trucker-btn svg {
  transition: transform 0.3s ease;
}

.trucker-btn:hover svg {
  transform: scale(1.1);
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.header-brand {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
  animation: fadeInUp 1.2s ease-out 0.3s both;
}

.company-logo {
  max-height: 80px;
  max-width: 200px;
  object-fit: contain;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px;
  transition: all 0.4s ease;
  animation: logoFloat 3s ease-in-out infinite;
}

.company-logo:hover {
  transform: scale(1.05) rotate(2deg);
  filter: drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.4));
}

.brand-text {
  text-align: center;
}

/* Mobile-first: Smaller title */
.home-header h1 {
  font-size: clamp(2rem, 8vw, 3rem);
  margin-bottom: 0.5rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: textGlow 2s ease-in-out infinite alternate;
  line-height: 1.1;
}

.typewriter-container {
  font-size: clamp(1.2rem, 4vw, 1.8rem);
  font-weight: 500;
  margin-bottom: 1rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
  animation: fadeInUp 1.8s ease-out 0.9s both;
  overflow: hidden;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  text-align: center;
  white-space: nowrap;
  position: relative;
  flex-shrink: 0;
}

.typewriter-prefix {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  flex-shrink: 0;
}

.typewriter-space {
  color: transparent;
  width: 0.3ch;
  flex-shrink: 0;
}

.typewriter-text {
  background: linear-gradient(135deg, #FFA000 0%, #FFB74D 30%, #FFA000 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
  width: 20ch;
  position: relative;
  overflow: visible;
  text-align: left;
  filter: drop-shadow(0 0 8px rgba(255, 160, 0, 0.3));
  white-space: nowrap;
  display: inline-block;
  flex-shrink: 0;
}

.typewriter-cursor {
  color: #FFA000;
  font-weight: 400;
  font-size: 1em;
  animation: blink 1.2s infinite;
  margin-left: 2px;
  display: inline-block;
  position: relative;
  filter: drop-shadow(0 0 4px rgba(255, 160, 0, 0.5));
  z-index: 10;
  min-width: 1ch;
}

.home-header p {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
  animation: fadeIn 1.5s ease-out 1.2s both;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.9;
  }
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

@keyframes textGlow {
  from {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }
  to {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.3);
  }
}

/* Mobile-first: Compact main section */
.home-main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  position: relative;
  z-index: 1;
  min-height: 0;
}

/* Desktop: Hide overflow */
@media (min-width: 769px) {
  .home-main {
    overflow: hidden;
  }
}

/* Mobile: Allow scrolling */
@media (max-width: 768px) {
  .home-main {
    overflow-y: auto;
    overflow-x: hidden;
    align-items: flex-start;
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
}

/* Mobile-first: Single column navigation */
.navigation-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 100%;
  width: 100%;
  animation: fadeInUp 1.5s ease-out 0.9s both;
}

/* Mobile-first: Compact nav cards */
.nav-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  display: flex;
  align-items: center;
  gap: 1rem;
  border-left: 4px solid;
  min-height: 80px;
  position: relative;
  overflow: hidden;
  animation: slideInCard 0.8s ease-out both;
}

button.nav-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  background: rgba(255,255,255,0.85); /* soft card look, not fully opaque */
  border: 2px solid #e0e0e0;
  border-left: 8px solid var(--card-border, #1976d2);
  border-radius: 14px;
  box-shadow: 0 4px 16px rgba(30,136,229,0.07), 0 1.5px 6px rgba(0,0,0,0.04);
  padding: 1.5rem 1.25rem 1.25rem 1.25rem;
  min-height: 160px;
  cursor: pointer;
  transition: box-shadow 0.2s, border-color 0.2s, background 0.2s;
  outline: none;
  width: 100%;
  text-align: left;
  position: relative;
  gap: 0.5rem;
  appearance: none;
  font: inherit;
  color: inherit;
  box-sizing: border-box;
  overflow: hidden;
}
button.nav-card:focus, button.nav-card:hover {
  box-shadow: 0 8px 24px rgba(30,136,229,0.13), 0 2px 8px rgba(0,0,0,0.08);
  border-color: #1976d2;
  background: rgba(30,136,229,0.08);
}

.nav-card-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  font-size: 2.2rem;
  margin-bottom: 0.5rem;
}

.nav-card-icon svg {
  transition: transform 0.3s ease;
}

.nav-card:hover .nav-card-icon {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.1);
}

.nav-card:hover .nav-card-icon svg {
  transform: scale(1.1);
}

.nav-card:nth-child(1) { animation-delay: 1.2s; }
.nav-card:nth-child(2) { animation-delay: 1.4s; }
.nav-card:nth-child(3) { animation-delay: 1.6s; }

.nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.nav-card:hover::before {
  left: 100%;
}

.nav-card:hover {
  transform: translateY(-8px) scale(1.02);
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 255, 255, 0.4);
}

.nav-card:active {
  transform: translateY(-4px) scale(0.98);
}

@keyframes slideInCard {
  from {
    opacity: 0;
    transform: translateY(50px) rotateX(20deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) rotateX(0deg);
  }
}

.nav-card-content {
  flex: 1;
  position: relative;
  z-index: 2;
  min-width: 0;
}

.nav-card-content h2 {
  font-size: 1.2rem;
  margin: 0 0 0.25rem 0;
  font-weight: 600;
}

.nav-card-content p {
  font-size: 1rem;
  margin: 0;
  color: #666;
}

.nav-card-arrow {
  position: absolute;
  right: 1.25rem;
  bottom: 1.25rem;
  font-size: 1.5rem;
  color: #1976d2;
  opacity: 0.7;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  z-index: 2;
  animation: arrowPulse 2s ease-in-out infinite;
}

.nav-card:hover .nav-card-content h2 {
  transform: translateX(5px);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.nav-card:hover .nav-card-content p {
  opacity: 1;
  transform: translateX(3px);
}

.nav-card:hover .nav-card-arrow {
  opacity: 1;
  transform: translateX(8px) scale(1.2);
  animation: arrowBounce 0.6s ease-in-out;
}

@keyframes arrowPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes arrowBounce {
  0%, 100% { transform: translateX(8px) scale(1.2); }
  50% { transform: translateX(12px) scale(1.3); }
}

.home-footer {
  text-align: center;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
  animation: fadeIn 2s ease-out 2s both;
}

.home-footer p {
  opacity: 0.7;
  font-size: 0.9rem;
  transition: opacity 0.3s ease;
}

.home-footer:hover p {
  opacity: 1;
}

@media (max-width: 768px) {
  .home-header {
    padding: 2rem 1rem;
  }

  .header-controls {
    top: 0.5rem;
    right: 0.5rem;
    gap: 0.75rem;
  }

  .toggle-switch {
    gap: 0.25rem;
  }

  .toggle-slider {
    width: 36px;
    height: 20px;
  }

  .toggle-slider::before {
    width: 14px;
    height: 14px;
    top: 2px;
    left: 2px;
  }

  .toggle-switch input:checked + .toggle-slider::before {
    transform: translateX(16px);
  }

  .toggle-label {
    font-size: 0.75rem;
  }

  .trucker-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    gap: 0.25rem;
  }

  .trucker-btn svg {
    width: 16px;
    height: 16px;
  }

  .home-header h1 {
    font-size: clamp(1.8rem, 6vw, 2.5rem);
  }

  .typewriter-container {
    font-size: clamp(1.1rem, 3.5vw, 1.4rem);
    height: 2.2rem;
    flex-wrap: nowrap;
    justify-content: center;
    padding: 0 1rem;
    overflow: hidden;
    white-space: nowrap;
    max-width: 100vw;
  }

  .typewriter-text {
    width: 17ch;
    white-space: nowrap;
    text-align: left;
    overflow: visible;
  }

  .navigation-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .nav-card {
    padding: 1.5rem;
    min-height: 100px;
  }

  .nav-card-content h2 {
    font-size: 1.3rem;
  }
}

/* Tablet and larger screens */
@media (min-width: 600px) {
  .home-header {
    padding: 2rem 2rem 1.5rem;
  }

  .home-main {
    padding: 2rem;
  }

  .navigation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
  }

  .nav-card {
    padding: 2rem;
    gap: 1.5rem;
    border-radius: 16px;
    min-height: 120px;
  }

  .nav-card-content h2 {
    font-size: 1.5rem;
  }

  .header-controls {
    gap: 1rem;
  }

  .toggle-label {
    display: block;
  }

  .trucker-btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
}

/* Desktop screens */
@media (min-width: 1024px) {
  .home-header {
    padding: 3rem 2rem 2rem;
  }

  .navigation-grid {
    gap: 2.5rem;
  }

  .nav-card {
    padding: 2.5rem;
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255,255,255,0.85);
  color: #333;
  border-radius: 20px;
  padding: 4px 12px;
  font-size: 0.95rem;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  margin-right: 0.5rem;
}
.user-name {
  font-weight: 600;
  color: #1976d2;
}
.user-role {
  color: #888;
  font-size: 0.85em;
}
.logout-btn {
  background: none;
  border: none;
  color: #c62828;
  font-size: 1.1em;
  cursor: pointer;
  margin-left: 0.25rem;
  padding: 2px 6px;
  border-radius: 6px;
  transition: background 0.2s;
}
.logout-btn:hover {
  background: #ffeaea;
}

/* Responsive: stack header controls if needed */
@media (max-width: 600px) {
  .header-controls {
    flex-wrap: wrap;
    gap: 0.5rem;
    right: 0.5rem;
    top: 0.5rem;
  }
  .user-info {
    font-size: 0.85rem;
    padding: 2px 8px;
  }
}

.navigation-grid.no-overlap-fix {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  align-items: stretch;
  width: 100%;
  margin: 0 auto;
  max-width: 900px;
  box-sizing: border-box;
}

.nav-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1.5rem;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(33, 150, 243, 0.08);
  border-left: 8px solid #1976d2;
  padding: 2rem 2rem 2rem 1.5rem;
  cursor: pointer;
  transition: box-shadow 0.2s, transform 0.15s;
  min-width: 0;
  width: 100%;
  border: none;
  outline: none;
  text-align: left;
  position: relative;
  z-index: 1;
}

.nav-card:focus,
.nav-card:hover {
  box-shadow: 0 6px 24px rgba(33, 150, 243, 0.18);
  transform: translateY(-2px) scale(1.01);
}

.nav-card-content {
  flex: 1;
  min-width: 0;
}

@media (max-width: 900px) {
  .navigation-grid.no-overlap-fix {
    grid-template-columns: 1fr;
    gap: 1.2rem;
    padding: 1rem 0;
  }
  .nav-card {
    padding: 1.2rem 1.2rem 1.2rem 1rem;
  }
}

@media (max-width: 600px) {
  .navigation-grid.no-overlap-fix {
    gap: 0.7rem;
    padding: 0.5rem 0;
  }
  .nav-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.7rem;
    padding: 1rem;
  }
  .nav-card-arrow {
    margin-left: 0;
    margin-top: 0.5rem;
  }
}

/* Defensive fix: prevent overlap with header controls */
.header-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: flex-end;
  min-width: 0;
}

.user-info {
  margin-right: 1rem;
  font-size: 0.95rem;
  color: #444;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
