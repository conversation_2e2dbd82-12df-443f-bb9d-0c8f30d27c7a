// Script to set up mock trucker login
(function() {
  // Create mock auth data
  const mockAuthData = {
    driverName: "<PERSON>",
    driverCode: "123",
    loginTime: new Date().toISOString(),
    token: "mock-token-" + Date.now()
  };
  
  // Set localStorage values
  localStorage.setItem('truckerAuth', JSON.stringify(mockAuthData));
  localStorage.setItem('truckerInfo', JSON.stringify(mockAuthData));
  localStorage.setItem('developerMode', JSON.stringify(true));
  
  console.log('Mock trucker login set up successfully');
  console.log('Auth data:', mockAuthData);
  
  // Redirect to trucker material ticketing page
  window.location.href = '/trucker-material-ticketing';
})();
