{"name": "materialflow-dashboard-server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["material", "ticketing", "dashboard", "workflow"], "author": "", "license": "ISC", "description": "MaterialFlow Dashboard - Professional Material Ticketing & Workflow Management API", "dependencies": {"@turf/turf": "^7.2.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^5.1.0", "firebase-admin": "^13.4.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "multer": "^2.0.0", "nodemailer": "^7.0.3", "site-for-kevin-root": "file:..", "uuid": "^11.1.0"}, "devDependencies": {"gh-pages": "^6.3.0", "nodemon": "^3.1.10"}}