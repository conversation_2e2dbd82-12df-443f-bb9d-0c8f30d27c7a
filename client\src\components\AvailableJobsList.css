.available-jobs-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-height: 100vh;
  background: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-brand {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-title h1 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.page-title p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.jobs-banner {
  background-color: #1E88E5;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  margin-top: 10px;
  display: inline-block;
  font-weight: 500;
}

.highlight-text {
  font-size: 1.1rem;
  font-weight: 600;
}

.logout-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f5f5f5;
  border: none;
  border-radius: 8px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-button:hover {
  background: #e0e0e0;
  color: #333;
}

.jobs-content {
  max-width: 1000px;
  margin: 0 auto;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 8px;
  color: #c62828;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
}

.no-jobs-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  text-align: center;
  color: #666;
}

.no-jobs-message svg {
  margin-bottom: 1rem;
  color: #999;
}

.no-jobs-message h3 {
  margin: 0 0 0.5rem;
  font-size: 1.2rem;
  color: #333;
}

.no-jobs-message p {
  margin: 0;
  font-size: 0.9rem;
}

.jobs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.job-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.job-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.job-header {
  padding: 1.5rem 1.5rem 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.job-header h3 {
  margin: 0 0 0.5rem;
  font-size: 1.2rem;
  color: #333;
}

.client-name {
  display: inline-block;
  font-size: 0.9rem;
  color: #666;
  background: #f5f5f5;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.job-details {
  padding: 1rem 1.5rem;
}

.detail-item {
  margin-bottom: 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.detail-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.detail-value {
  font-size: 0.9rem;
  color: #333;
  text-align: right;
  word-break: break-word;
}

.accept-job-button {
  width: 100%;
  padding: 1rem;
  background: #1E88E5;
  color: white;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.accept-job-button:hover {
  background: #1565C0;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #f5f5f5;
  border: none;
  border-radius: 8px;
  color: #666;
  font-size: 0.9rem;
  cursor: pointer;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.refresh-button:hover {
  background: #e0e0e0;
  color: #333;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.spinner-large {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #1E88E5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .available-jobs-container {
    padding: 1rem;
  }

  .jobs-grid {
    grid-template-columns: 1fr;
  }
}
