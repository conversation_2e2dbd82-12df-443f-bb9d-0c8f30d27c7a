import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup } from 'react-leaflet';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { Camera, Upload, CheckCircle, Truck, MapPin } from 'lucide-react'; // Remove this line if unused
import SignatureCanvas from 'react-signature-canvas';
import 'leaflet/dist/leaflet.css';

interface Location {
  _id?: string;
  name: string;
  address: string;
  coordinates: {
    lat: number;
    lng: number;
  };
}

interface Load {
  materialType: string;
  quantity: number;
  unit: string;
}

interface PurchaseOrder {
  _id: string;
  jobDetails: string;
  startingLocation: Location;
  deliveryLocations: Location[];
  loads: Load[];
  status: string;
}

interface Transaction {
  _id: string;
  purchaseOrderId: string;
  status: string;
  materialType: string;
  unit: string;
  pickup: {
    location: Location;
    verification?: {
      photoUrl: string;
      geolocation: {
        lat: number;
        lng: number;
      };
      timestamp: string;
      signature: {
        name: string;
        signatureImageUrl: string;
      };
      weighTicketNumber?: string;
      weight?: number;
      notes?: string;
    };
  };
  dump: {
    location: Location;
    verification?: {
      photoUrl: string;
      geolocation: {
        lat: number;
        lng: number;
      };
      timestamp: string;
      signature: {
        name: string;
        signatureImageUrl: string;
      };
      weighTicketNumber: string;
      weight: number;
      notes?: string;
    };
  };
}

const TruckerTransactionFlow: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  
  // State for current location
  const [currentLocation, setCurrentLocation] = useState<{lat: number; lng: number} | null>(null);
  const [locationError, setLocationError] = useState<string>('');
  
  // State for available POs and active transactions
  const [availablePOs, setAvailablePOs] = useState<PurchaseOrder[]>([]);
  const [activeTransactions, setActiveTransactions] = useState<Transaction[]>([]);
  const [selectedPO, setSelectedPO] = useState<PurchaseOrder | null>(null);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  
  // State for transaction flow
  const [flowStep, setFlowStep] = useState<'list' | 'pickup-start' | 'pickup-complete' | 'dump-start' | 'dump-complete'>('list');
  
  // State for verification data
  const [photo, setPhoto] = useState<File | null>(null);
  const [photoPreview, setPhotoPreview] = useState<string>('');
  const [signerName, setSignerName] = useState<string>('');
  const [signatureRef, setSignatureRef] = useState<any>(null);
  const [weighTicketNumber, setWeighTicketNumber] = useState<string>('');
  const [weight, setWeight] = useState<number>(0);
  const [notes, setNotes] = useState<string>('');
  const [selectedMaterial, setSelectedMaterial] = useState<string>('');
  const [selectedDumpLocation, setSelectedDumpLocation] = useState<string>('');
  
  // Get current location
  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setCurrentLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
          setLocationError('');
        },
        (error) => {
          setLocationError('Error getting location: ' + error.message);
        }
      );
    } else {
      setLocationError('Geolocation is not supported by this browser.');
    }
  };
  
  // Fetch available POs
  const fetchAvailablePOs = async () => {
    if (!currentLocation) return;
    
    try {
      const response = await axios.get('/api/transactions/available-pos', {
        params: {
          lat: currentLocation.lat,
          lng: currentLocation.lng
        }
      });
      
      setAvailablePOs(response.data);
    } catch (error) {
      console.error('Error fetching available POs:', error);
    }
  };
  
  // Fetch active transactions
  const fetchActiveTransactions = async () => {
    try {
      const response = await axios.get('/api/transactions/trucker/open');
      setActiveTransactions(response.data);
    } catch (error) {
      console.error('Error fetching active transactions:', error);
    }
  };
  
  // Start pickup process
  const startPickup = async () => {
    if (!selectedPO || !selectedMaterial || !selectedDumpLocation) {
      alert('Please select a purchase order, material type, and dump location');
      return;
    }
    
    try {
      const response = await axios.post('/api/transactions/pickup/start', {
        purchaseOrderId: selectedPO._id,
        materialType: selectedMaterial,
        locationId: selectedDumpLocation
      });
      
      setSelectedTransaction(response.data.transaction);
      setFlowStep('pickup-complete');
      fetchActiveTransactions();
    } catch (error) {
      console.error('Error starting pickup:', error);
      alert('Error starting pickup process');
    }
  };
  
  // Complete pickup process
  const completePickup = async () => {
    if (!selectedTransaction || !photo || !signerName || !signatureRef || !currentLocation) {
      alert('Please fill all required fields');
      return;
    }
    
    try {
      // Upload photo
      const formData = new FormData();
      formData.append('photo', photo);
      
      const uploadResponse = await axios.post('/api/upload', formData);
      const photoUrl = uploadResponse.data.url;
      
      // Get signature as image
      const signatureImageUrl = signatureRef.toDataURL();
      
      // Complete pickup
      await axios.put(`/api/transactions/pickup/${selectedTransaction._id}/complete`, {
        photoUrl,
        lat: currentLocation.lat,
        lng: currentLocation.lng,
        signerName,
        signatureImageUrl,
        weighTicketNumber,
        weight,
        notes
      });
      
      // Reset form
      resetForm();
      
      // Update transaction list and go to list view
      fetchActiveTransactions();
      setFlowStep('list');
    } catch (error) {
      console.error('Error completing pickup:', error);
      alert('Error completing pickup process');
    }
  };
  
  // Start dump process
  const startDump = async (transactionId: string) => {
    try {
      const response = await axios.put(`/api/transactions/dump/${transactionId}/start`);
      
      setSelectedTransaction(response.data.transaction);
      setFlowStep('dump-complete');
    } catch (error) {
      console.error('Error starting dump:', error);
      alert('Error starting dump process');
    }
  };
  
  // Complete dump process
  const completeDump = async () => {
    if (!selectedTransaction || !photo || !signerName || !signatureRef || !currentLocation || !weighTicketNumber || weight <= 0) {
      alert('Please fill all required fields');
      return;
    }
    
    try {
      // Upload photo
      const formData = new FormData();
      formData.append('photo', photo);
      
      const uploadResponse = await axios.post('/api/upload', formData);
      const photoUrl = uploadResponse.data.url;
      
      // Get signature as image
      const signatureImageUrl = signatureRef.toDataURL();
      
      // Complete dump
      await axios.put(`/api/transactions/dump/${selectedTransaction._id}/complete`, {
        photoUrl,
        lat: currentLocation.lat,
        lng: currentLocation.lng,
        signerName,
        signatureImageUrl,
        weighTicketNumber,
        weight,
        notes
      });
      
      // Reset form
      resetForm();
      
      // Update transaction list and go to list view
      fetchActiveTransactions();
      setFlowStep('list');
    } catch (error) {
      console.error('Error completing dump:', error);
      alert('Error completing dump process');
    }
  };
  
  // Reset form
  const resetForm = () => {
    setPhoto(null);
    setPhotoPreview('');
    setSignerName('');
    if (signatureRef) signatureRef.clear();
    setWeighTicketNumber('');
    setWeight(0);
    setNotes('');
    setSelectedMaterial('');
    setSelectedDumpLocation('');
  };
  
  // Handle photo upload
  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setPhoto(file);
      
      const reader = new FileReader();
      reader.onloadend = () => {
        setPhotoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Initialize
  useEffect(() => {
    getCurrentLocation();
    
    // Set up periodic location updates
    const locationInterval = setInterval(getCurrentLocation, 60000); // Update every minute
    
    return () => {
      clearInterval(locationInterval);
    };
  }, []);
  
  // Fetch data when location changes
  useEffect(() => {
    if (currentLocation) {
      fetchAvailablePOs();
      fetchActiveTransactions();
    }
  }, [currentLocation]);
  
  return (
    <div className="transaction-flow-container">
      <h1>{t('Trucker Transaction Flow')}</h1>
      
      {locationError && (
        <div className="error-message">
          {locationError}
        </div>
      )}
      
      {currentLocation && (
        <div className="location-info">
          <p>
            {t('Current Location')}: {currentLocation.lat.toFixed(6)}, {currentLocation.lng.toFixed(6)}
          </p>
        </div>
      )}
      
      {flowStep === 'list' && (
        <div className="transaction-list">
          <h2>{t('Active Transactions')}</h2>
          {activeTransactions.length === 0 ? (
            <p>{t('No active transactions')}</p>
          ) : (
            <div className="transactions-grid">
              {activeTransactions.map(transaction => (
                <div key={transaction._id} className="transaction-card">
                  <h3>{t('Transaction')}: {transaction._id}</h3>
                  <p>{t('Material')}: {transaction.materialType}</p>
                  <p>{t('Status')}: {transaction.status}</p>
                  
                  {transaction.status === 'pickup_completed' && (
                    <button 
                      className="action-button"
                      onClick={() => {
                        setSelectedTransaction(transaction);
                        startDump(transaction._id);
                      }}
                    >
                      {t('Start Dump')}
                    </button>
                  )}
                </div>
              ))}
            </div>
          )}
          
          <h2>{t('Available Purchase Orders')}</h2>
          {availablePOs.length === 0 ? (
            <p>{t('No available purchase orders in your area')}</p>
          ) : (
            <div className="po-grid">
              {availablePOs.map(po => (
                <div key={po._id} className="po-card">
                  <h3>{t('PO')}: {po._id}</h3>
                  <p>{t('Job Details')}: {po.jobDetails}</p>
                  <p>{t('Status')}: {po.status}</p>
                  
                  <button 
                    className="action-button"
                    onClick={() => {
                      setSelectedPO(po);
                      setFlowStep('pickup-start');
                    }}
                  >
                    {t('Start Pickup')}
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
      
      {flowStep === 'pickup-start' && selectedPO && (
        <div className="pickup-start-form">
          <h2>{t('Start Pickup Process')}</h2>
          
          <div className="form-group">
            <label>{t('Select Material Type')}:</label>
            <select 
              value={selectedMaterial} 
              onChange={(e) => setSelectedMaterial(e.target.value)}
              required
            >
              <option value="">{t('Select Material')}</option>
              {selectedPO.loads.map((load, index) => (
                <option key={index} value={load.materialType}>
                  {load.materialType} ({load.quantity} {load.unit})
                </option>
              ))}
            </select>
          </div>
          
          <div className="form-group">
            <label>{t('Select Dump Location')}:</label>
            <select 
              value={selectedDumpLocation} 
              onChange={(e) => setSelectedDumpLocation(e.target.value)}
              required
            >
              <option value="">{t('Select Location')}</option>
              {selectedPO.deliveryLocations.map((location, index) => (
                <option key={index} value={location._id}>
                  {location.name} - {location.address}
                </option>
              ))}
            </select>
          </div>
          
          <div className="map-container">
            <h3>{t('Pickup Location')}</h3>
            {currentLocation && (
              <MapContainer 
                center={[selectedPO.startingLocation.coordinates.lat, selectedPO.startingLocation.coordinates.lng]} 
                zoom={13} 
                style={{ height: '300px', width: '100%' }}
              >
                <TileLayer
                  url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                />
                <Marker position={[selectedPO.startingLocation.coordinates.lat, selectedPO.startingLocation.coordinates.lng]}>
                  <Popup>
                    {selectedPO.startingLocation.name}<br />
                    {selectedPO.startingLocation.address}
                  </Popup>
                </Marker>
                {currentLocation && (
                  <Marker position={[currentLocation.lat, currentLocation.lng]}>
                    <Popup>{t('Your Location')}</Popup>
                  </Marker>
                )}
              </MapContainer>
            )}
          </div>
          
          <div className="button-group">
            <button 
              className="cancel-button"
              onClick={() => {
                setSelectedPO(null);
                setFlowStep('list');
              }}
            >
              {t('Cancel')}
            </button>
            <button 
              className="action-button"
              onClick={startPickup}
              disabled={!selectedMaterial || !selectedDumpLocation}
            >
              {t('Start Pickup')}
            </button>
          </div>
        </div>
      )}
      
      {(flowStep === 'pickup-complete' || flowStep === 'dump-complete') && selectedTransaction && (
        <div className="verification-form">
          <h2>
            {flowStep === 'pickup-complete' 
              ? t('Complete Pickup Process') 
              : t('Complete Dump Process')}
          </h2>
          
          <div className="form-group">
            <label>{t('Take Photo')}:</label>
            <div className="photo-upload">
              <input
                type="file"
                accept="image/*"
                onChange={handlePhotoChange}
                required
              />
              {photoPreview && (
                <div className="photo-preview">
                  <img src={photoPreview} alt="Preview" />
                </div>
              )}
            </div>
          </div>
          
          <div className="form-group">
            <label>{t('Signer Name')}:</label>
            <input
              type="text"
              value={signerName}
              onChange={(e) => setSignerName(e.target.value)}
              placeholder={t('Enter name')}
              required
            />
          </div>
          
          <div className="form-group">
            <label>{t('Signature')}:</label>
            <div className="signature-pad">
              <SignatureCanvas
                ref={(ref: React.Ref<any>) => {setSignatureRef(ref)}}
                canvasProps={{
                  width: 500,
                  height: 200,
                  className: 'signature-canvas'
                }}
              />
              <button 
                className="clear-button"
                onClick={() => signatureRef.clear()}
              >
                {t('Clear')}
              </button>
            </div>
          </div>
          
          <div className="form-group">
            <label>{t('Weigh Ticket Number')}:</label>
            <input
              type="text"
              value={weighTicketNumber}
              onChange={(e) => setWeighTicketNumber(e.target.value)}
              placeholder={t('Enter ticket number')}
              required={flowStep === 'dump-complete'}
            />
          </div>
          
          {flowStep === 'dump-complete' && (
            <div className="form-group">
              <label>{t('Weight')}:</label>
              <input
                type="number"
                value={weight}
                onChange={(e) => setWeight(parseFloat(e.target.value))}
                placeholder={t('Enter weight')}
                required
              />
              <span>{selectedTransaction.unit}</span>
            </div>
          )}
          
          <div className="form-group">
            <label>{t('Notes')}:</label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder={t('Enter notes (optional)')}
            />
          </div>
          
          <div className="button-group">
            <button 
              className="cancel-button"
              onClick={() => {
                resetForm();
                setSelectedTransaction(null);
                setFlowStep('list');
              }}
            >
              {t('Cancel')}
            </button>
            <button 
              className="action-button"
              onClick={flowStep === 'pickup-complete' ? completePickup : completeDump}
            >
              {flowStep === 'pickup-complete' 
                ? t('Complete Pickup') 
                : t('Complete Dump')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TruckerTransactionFlow;
