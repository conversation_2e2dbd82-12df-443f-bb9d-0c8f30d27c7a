/* Ticket Detail Modal Styles */
.ticket-detail-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.ticket-detail-modal {
  background: white;
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 16px;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-content h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.ticket-type-badge {
  display: flex;
  gap: 8px;
}

.manual-badge, .upload-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.manual-badge {
  background-color: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.upload-badge {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.close-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 24px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Modal Content */
.modal-content {
  padding: 24px;
}

.detail-section {
  margin-bottom: 32px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #667eea;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.detail-item.clickable {
  cursor: pointer;
}

.detail-item.clickable:hover {
  background: #e3f2fd;
  border-color: #2196f3;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
}

.detail-item .label {
  font-weight: 600;
  color: #555;
  margin-right: 12px;
}

.detail-item .value {
  font-weight: 500;
  color: #333;
  text-align: right;
  word-break: break-word;
}

.status-value {
  color: white !important;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ticket-number {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #2196f3;
}

.material-type {
  color: #ff9800;
  font-weight: 600;
}

.quantity {
  color: #4caf50;
  font-weight: 600;
}

.driver-name {
  color: #9c27b0;
  font-weight: 600;
}

/* Description Section */
.description-content {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
}

.description-content.clickable {
  cursor: pointer;
}

.description-content.clickable:hover {
  background: #e3f2fd;
  border-color: #2196f3;
}

.description-content p {
  margin: 0;
  line-height: 1.6;
  color: #333;
}

/* Image Preview */
.image-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 16px;
  min-height: 200px;
}

.ticket-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ticket-image:hover {
  transform: scale(1.02);
}

/* Modal Actions */
.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 16px 24px 24px;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

.edit-button, .delete-button, .close-action-button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.edit-button {
  background: #2196f3;
  color: white;
}

.edit-button:hover {
  background: #1976d2;
  transform: translateY(-1px);
}

.delete-button {
  background: #f44336;
  color: white;
}

.delete-button:hover {
  background: #d32f2f;
  transform: translateY(-1px);
}

.close-action-button {
  background: #6c757d;
  color: white;
}

.close-action-button:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

/* Delete Confirmation */
.delete-confirm-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
}

.delete-confirm-modal {
  background: white;
  padding: 24px;
  border-radius: 8px;
  max-width: 400px;
  text-align: center;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.delete-confirm-modal h3 {
  margin: 0 0 16px 0;
  color: #f44336;
}

.delete-confirm-modal p {
  margin: 0 0 24px 0;
  color: #666;
  line-height: 1.5;
}

.confirm-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.confirm-delete, .cancel-delete {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-delete {
  background: #f44336;
  color: white;
}

.confirm-delete:hover {
  background: #d32f2f;
}

.cancel-delete {
  background: #e0e0e0;
  color: #333;
}

.cancel-delete:hover {
  background: #bdbdbd;
}

/* Mobile Responsiveness */
@media (max-width: 600px) {
  .ticket-detail-modal-overlay {
    padding: 10px;
  }
  
  .ticket-detail-modal {
    max-height: 95vh;
  }
  
  .modal-header {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .header-content h2 {
    font-size: 1.3rem;
  }
  
  .modal-content {
    padding: 16px;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .detail-item .value {
    text-align: left;
  }
  
  .modal-actions {
    flex-direction: column;
    padding: 16px;
  }
}
