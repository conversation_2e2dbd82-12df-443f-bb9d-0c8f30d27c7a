{"name": "materialflow-dashboard-client", "private": true, "version": "0.0.0", "type": "module", "homepage": "https://ThomasvRos28.github.io/Site-for-Kevin", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "predeploy": "npm run build", "deploy": "gh-pages -d dist"}, "dependencies": {"@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@turf/turf": "^7.2.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "firebase": "^11.8.1", "i18next": "^23.10.0", "i18next-browser-languagedetector": "^8.1.0", "leaflet": "^1.9.4", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^14.0.5", "react-leaflet": "4.2.1", "react-router-dom": "^6.22.2", "react-signature-canvas": "^1.1.0-alpha.2", "site-for-kevin-root": "file:..", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/leaflet": "^1.9.8", "@types/node": "^20.11.24", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-react-swc": "^3.10.0", "autoprefixer": "^10.4.18", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "gh-pages": "^6.3.0", "globals": "^16.0.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.1.4"}}