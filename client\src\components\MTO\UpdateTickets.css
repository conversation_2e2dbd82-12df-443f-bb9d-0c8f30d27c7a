.update-tickets {
  padding: 0;
}

.tickets-count {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Filters Section */
.filters-section {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.filters-section h3 {
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
  outline: none;
  border-color: #2196f3;
}

.filter-actions {
  display: flex;
  align-items: end;
}

/* Table Styles */
.table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
  vertical-align: middle;
}

.table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.table tr:hover {
  background: #f8f9fa;
}

.table td {
  color: #555;
}

/* Edit Inputs */
.edit-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.edit-input:focus {
  outline: none;
  border-color: #2196f3;
}

.quantity-edit {
  display: flex;
  gap: 0.5rem;
}

.quantity-input {
  flex: 2;
}

.unit-select {
  flex: 1;
}

/* Actions */
.row-actions,
.edit-actions {
  display: flex;
  gap: 0.5rem;
  white-space: nowrap;
}

.edit-actions {
  flex-direction: column;
}

/* Status */
.status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  white-space: nowrap;
}

/* No Tickets */
.no-tickets {
  text-align: center;
  padding: 3rem;
  color: #666;
  font-size: 1.1rem;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .filters-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
  
  .table-container {
    overflow-x: auto;
  }
  
  .table {
    min-width: 1000px;
  }
}

@media (max-width: 768px) {
  .filters-section {
    padding: 1.5rem;
  }
  
  .filters-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .filter-actions {
    justify-content: center;
  }
  
  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.9rem;
  }
  
  .row-actions,
  .edit-actions {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .quantity-edit {
    flex-direction: column;
  }
}
