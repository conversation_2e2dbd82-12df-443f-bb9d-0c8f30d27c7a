/* Mobile-first design */
.trucker-material-ticketing-container {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #2196F3 0%, #2196F3 30%, #1976D2 50%, #0D47A1 70%, #FF8F00 85%, #FFA000 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  padding: 1rem;
  position: relative;
  box-sizing: border-box;
}

.trucker-material-ticketing-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.15"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.15"/><circle cx="80" cy="30" r="0.5" fill="white" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
  animation: float 20s ease-in-out infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

.ticketing-banner {
  background-color: #ff9800;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  margin-top: 10px;
  display: inline-block;
  font-weight: 500;
}

.highlight-text {
  font-size: 1.1rem;
  font-weight: 600;
}

.location-status {
  margin-top: 1rem;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-ok {
  color: #4CAF50;
  font-weight: 600;
  background: rgba(76, 175, 80, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.status-error {
  color: #F44336;
  font-weight: 600;
  background: rgba(244, 67, 54, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.section-title {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 5px;
  border-bottom: 2px solid #ff9800;
  padding-bottom: 5px;
  display: inline-block;
}

.section-description {
  color: #666;
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.po-list-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
  color: white;
  animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.po-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.25rem;
  margin-bottom: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.po-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.po-item.selected {
  border: 2px solid #FFA000;
  box-shadow: 0 0 0 3px rgba(255, 160, 0, 0.2);
}

.po-item.within-geofence {
  position: relative;
}

.po-item.within-geofence::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 40px 40px 0;
  border-color: transparent #4CAF50 transparent transparent;
  z-index: 1;
}

.po-item.within-geofence::after {
  content: '✓';
  position: absolute;
  top: 5px;
  right: 8px;
  color: white;
  font-weight: bold;
  font-size: 14px;
  z-index: 2;
}

.can-container {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.can-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.can-header h4 {
  margin: 0;
  color: #ff9800;
  font-size: 1.1rem;
}

.remove-can-btn {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.remove-can-btn:hover {
  background-color: #d32f2f;
}

.add-can-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 10px;
}

.add-can-btn:hover {
  background-color: #388e3c;
}

.toggle-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  align-items: center;
  gap: 0.5rem;
}

.toggle-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  font-weight: 500;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-btn:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.toggle-btn.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Developer Mode Toggle */
.dev-mode-toggle {
  margin-left: auto;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  z-index: 10;
}

.dev-mode-toggle:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.dev-mode-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.9rem;
  user-select: none;
}

.dev-mode-label input {
  margin-right: 8px;
  accent-color: #FFA000;
  width: 16px;
  height: 16px;
}

.dev-mode-label span {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.dev-mode-label input:checked + span {
  color: #FFA000;
  font-weight: 600;
}

/* Geofence Status Indicator */
.geofence-status {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding: 12px 16px;
  border-radius: 12px;
  animation: fadeIn 0.3s ease-in-out;
  gap: 12px;
  border: 1px solid;
  transition: all 0.3s ease;
}

.geofence-status.within {
  background-color: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.geofence-status.outside {
  background-color: rgba(244, 67, 54, 0.1);
  border-color: rgba(244, 67, 54, 0.3);
  color: #F44336;
}

.geofence-status.dev-mode {
  background-color: rgba(255, 160, 0, 0.15);
  border-color: rgba(255, 160, 0, 0.3);
  color: #FFA000;
}

.geofence-status .status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.geofence-status .status-text {
  flex: 1;
  font-size: 1rem;
}

.dev-mode-indicator {
  font-weight: 600;
  color: #FFA000;
  margin-left: 8px;
  background-color: rgba(255, 160, 0, 0.15);
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Map Popup Styling */
.map-popup {
  padding: 5px;
  min-width: 200px;
}

.map-popup h3 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.popup-status {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  margin-bottom: 8px;
  gap: 8px;
}

.popup-status.within {
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.popup-status.outside {
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  color: #F44336;
}

.popup-status .status-icon {
  display: flex;
  align-items: center;
}

.popup-status .status-text {
  font-size: 13px;
  font-weight: 500;
}

.dev-mode-popup-note {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(255, 160, 0, 0.1);
  border: 1px solid rgba(255, 160, 0, 0.3);
  color: #FFA000;
  padding: 8px;
  border-radius: 6px;
  font-size: 12px;
  margin-top: 5px;
}

.dev-mode-popup-note strong {
  display: block;
  margin-bottom: 2px;
}

.ticketing-content {
  max-width: 800px;
  margin: 0 auto;
}

.ticket-form-container {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.ticket-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
  border-color: #888;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2196F3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  margin-top: 1rem;
}

.submit-button {
  width: 100%;
  padding: 1rem;
  background: #2196F3;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-button:hover:not(:disabled) {
  background: #1976D2;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
}

.submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.accept-btn, .complete-btn {
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  font-size: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
}

.accept-btn {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
}

.accept-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #388E3C 0%, #4CAF50 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.3);
}

.complete-btn {
  background: linear-gradient(135deg, #FFA000 0%, #FFB74D 100%);
}

.complete-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #F57C00 0%, #FFA000 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 160, 0, 0.3);
}

.accept-btn:disabled, .complete-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.completed-status {
  margin-top: 1rem;
  width: 100%;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  width: 100%;
  justify-content: center;
}

.status-badge.completed {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 8px;
  color: #c62828;
  font-size: 0.9rem;
}

.error-message svg {
  flex-shrink: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .trucker-material-ticketing-container {
    padding: 1rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .ticket-form-container {
    padding: 1.5rem;
  }
} 